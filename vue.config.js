const { defineConfig } = require('@vue/cli-service');

const TerserPlugin = require('terser-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
const { WebUpdateNotificationPlugin } = require('@plugin-web-update-notice/webpack');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const path = require('path');

const { name } = require('./package.json'); // 子应用名称
const isDev = process.env.VUE_APP_ENV === 'dev';
const isProd = process.env.VUE_APP_ENV === 'prod';
const PORT = 9000;

const resolve = (dir) => require('path').resolve(__dirname, dir);

module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: isDev ? '/' : '/biz',
  lintOnSave: false,
  runtimeCompiler: true,
  chainWebpack: (config) => {
    config.resolve.alias.set('@', resolve('src'));
    config.resolve.alias.set('ant-design-vue', '@syn/ant-design4-vue3');

    config.plugin('define').tap((defineOptions) => {
      Object.assign(defineOptions[0], {
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: isDev,
      });
      return defineOptions;
    });
    // svg
    // 排除默认对 src/assets/svgs 目录的 SVG 处理
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/svgs')) // 修改此处路径
      .end();

    // 创建 icons 规则，仅处理 src/assets/svgs 下的 SVG
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/svgs')) // 修改此处路径
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]', // 确保 symbolId 生成规则匹配
      });
  },
  configureWebpack: (config) => {
    if (isProd) {
      config.externals = {
        ...config.externals,
      };
    }
    config.optimization = {
      splitChunks: {
        chunks: 'all',
        minSize: 30000,
        maxSize: 500000,
        minChunks: 1,
        maxAsyncRequests: 30,
        maxInitialRequests: 30,
        automaticNameDelimiter: '~',
      },
      minimize: true,
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            compress: {
              drop_console: isProd,
              drop_debugger: true,
              // pure_funcs: ['console.log'], // 移除 console.log
            },
          },
          extractComments: false, // 可选，避免提取注释
        }),
      ],
    };
    config.output = {
      ...config.output,
      filename: `js/[name].[contenthash].bundle.js`,
      chunkFilename: `js/[name].[contenthash].chunk.js`,
      library: `${name}-[name]`,
      // 把微应用打包成 umd 库格式
      libraryTarget: 'umd',
      chunkLoadingGlobal: `webpackJsonp_${name}`,
    };
    if (!isDev) {
      config.plugins.push(
        new CompressionPlugin({
          test: /\.(js|css|html|svg|ttf|woff|woff2|eot)$/,
          algorithm: 'gzip',
          threshold: 10240,
          minRatio: 0.8,
          deleteOriginalAssets: false,
        }),
        // 版本更新检测
        new WebUpdateNotificationPlugin({
          // 控制台输出版本号
          logVersion: true,
          // 隐藏默认Notification
          hiddenDefaultNotification: true,
          checkImmediately: false,
          // 检测文件所在路径
          injectFileBase: `${process.env.VUE_APP_DZPT_URL}/biz/`,
          microApp: true,
        })
      );
    }
    config.optimization.minimizer = [...config.optimization.minimizer, new CssMinimizerPlugin()];
  },
  css: {
    extract: isDev
      ? false
      : {
          ignoreOrder: true,
          filename: `css/[name].[contenthash].bundle.css`,
          chunkFilename: 'css/[name].[contenthash].chunk.css',
        },
    loaderOptions: {
      stylus: {
        additionalData: (content) => {
          let importStyl = `@import "${path.resolve(__dirname, './src/styles/mixin.styl')}";`;
          return importStyl + content;
        },
      },
    },
  },
  devServer: {
    port: PORT,
    open: true,
    hot: true,
    host: '0.0.0.0',
    client: {
      overlay: false, // 不显示错误覆盖层
    },
    historyApiFallback: true,
    proxy: {
      '/test': {
        target: 'http://zhsj-test.haier.net',
        changeOrigin: true,
      },
      '/zgq': {
        target: 'https://ismtest.haier.net',
        changeOrigin: true,
        pathRewrite: {
          '^/zgq': '', // 移除 /zgq 前缀
        },
      },
      '/api': {
        target: 'https://syndz-test.haier.net',
        changeOrigin: true,
      },
    },
  },
});
