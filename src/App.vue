<script setup>
  import zhCN from 'ant-design-vue/es/locale/zh_CN';
  import 'dayjs/locale/zh-cn';
  import { _API_getUserInfoByToken } from '@/api/userInfo';
  import { onMounted, onUnmounted } from 'vue';
  import { useUserStore } from '@/store/modules/userStore';
  import NetworkDetector from '@/components/networkDetector';
  import { MAIN_PLATFORM_STORAGE_KEY } from '@/config/const';
  import { useModal } from '@/hooks/useModal';
  import { gioTrackReport } from '@/utils/gioTrack';
  const local = zhCN;
  const userStore = useUserStore();

  /**
   * @description 获取用户信息
   * @returns {Promise<void>}
   */
  async function getUserInfo() {
    _API_getUserInfoByToken().then((res) => {
      if (res.code === '0') {
        let frameUserInfo = res.data.frameUserInfo;
        let mainPlatUserInfo = frameUserInfo.accountInfo;
        userStore.setZjsjUserInfo(res?.data?.designUserInfo.accreditVo?.info);
        userStore.setUserInfo(mainPlatUserInfo);
        userStore.setPermissionInfo(frameUserInfo.permissionInfo);
      }
    });
  }

  /**
   * @description 监听 storage 变化
   */
  function handleStorageChange(e) {
    if (e.key === MAIN_PLATFORM_STORAGE_KEY) {
      let newValue = JSON.parse(e.newValue || '{}');
      let newAccountNo = newValue?.accountDTO?.accountNo;
      let oldAccountNo = userStore?.userInfo?.accountNo;
      let oldName = userStore?.userInfo?.accountName;
      let newName = newValue?.accountDTO?.accountName;
      if (oldAccountNo && newAccountNo && oldAccountNo !== newAccountNo) {
        useModal({
          title: '用户更换',
          content: `'检测到用户已由${oldName}切换为${newName}'`,
          closeable: false,
          hideCancel: true,
          onOk: () => {
            window.close();
          },
        });
      }
    }
  }

  onMounted(async () => {
    window.addEventListener('storage', handleStorageChange);

    await getUserInfo();
    gioTrackReport('CZHT30619');
  });

  onUnmounted(() => {
    window.removeEventListener('storage', handleStorageChange);
  });
</script>

<template>
  <a-config-provider :locale="local">
    <router-view />
    <!-- 全局网络检测组件 -->
    <NetworkDetector />
  </a-config-provider>
</template>

<style></style>
