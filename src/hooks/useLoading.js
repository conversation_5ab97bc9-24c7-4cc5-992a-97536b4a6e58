import { createVNode, ref, render } from 'vue';
import { Spin } from 'ant-design-vue';

// 全局loading实例管理
const loadingInstances = new Map();
let instanceId = 0;

/**
 * 创建loading实例
 * @param {Object} options - loading配置选项
 * @param {string} options.tip - loading提示文字
 * @param {string} options.size - loading大小 'small' | 'default' | 'large'
 * @param {number} options.delay - 延迟显示时间(ms)
 * @param {HTMLElement} options.target - 挂载目标元素，默认为document.body
 * @param {boolean} options.useCustomIcon - 是否使用自定义图标(synLoading.gif)
 * @returns {Object} loading实例
 */
function createLoading(options = {}) {
  const { tip = '加载中...', size = 'large', delay = 0, target = document.body, useCustomIcon = false } = options;

  // 生成唯一实例ID
  const id = ++instanceId;

  // 创建容器
  const container = document.createElement('div');
  container.className = 'global-loading-container';
  container.setAttribute('data-loading-id', id);
  container.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(2px);
  `;

  // 创建自定义indicator或使用默认Spin，tip 支持 VNode 或字符串
  let spinVNode;

  if (useCustomIcon) {
    // tip 为 VNode 直接用，否则用原来的结构
    let tipNode = null;
    if (tip && typeof tip === 'object' && tip.__v_isVNode) {
      tipNode = tip;
    } else if (tip) {
      tipNode = createVNode(
        'div',
        {
          style: {
            color: '#000',
            'font-family': 'PingFang SC',
            'font-size': '16px',
            'font-weight': '400',
            marginTop: '8px',
          },
        },
        tip
      );
    }
    spinVNode = createVNode(
      'div',
      {
        class: 'custom-loading-indicator',
        style: {
          textAlign: 'center',
          width: '240px',
          padding: '20px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          backgroundColor: '#fff',
          borderRadius: '16px',
        },
      },
      [
        createVNode('img', {
          src: require('@/assets/images/synLoading.gif'),
          alt: 'Loading',
          style: {
            width: size === 'small' ? '32px' : size === 'large' ? '150px' : '48px',
            height: size === 'small' ? '32px' : size === 'large' ? '150px' : '48px',
            marginBottom: tip ? '8px' : '0',
          },
        }),
        tipNode,
      ]
    );
  } else {
    // Spin 只支持字符串 tip，如果是 VNode，作为子节点插入
    if (tip && typeof tip === 'object' && tip.__v_isVNode) {
      spinVNode = createVNode(
        Spin,
        {
          spinning: true,
          size,
          delay,
          style: {
            maxHeight: 'none',
          },
        },
        {
          default: () => tip,
        }
      );
    } else {
      spinVNode = createVNode(Spin, {
        spinning: true,
        tip,
        size,
        delay,
        style: {
          maxHeight: 'none',
        },
      });
    }
  }

  // 渲染到容器
  render(spinVNode, container);

  // 添加到目标元素
  target.appendChild(container);

  // 创建实例对象
  const instance = {
    id,
    container,
    close() {
      if (container && container.parentNode) {
        container.parentNode.removeChild(container);
        loadingInstances.delete(id);
      }
    },
  };

  // 保存实例
  loadingInstances.set(id, instance);

  return instance;
}

/**
 * 销毁所有loading实例
 */
function destroyAll() {
  loadingInstances.forEach((instance) => {
    if (instance.container && instance.container.parentNode) {
      instance.container.parentNode.removeChild(instance.container);
    }
  });
  loadingInstances.clear();
}

/**
 * 全局loading hook
 * @returns {Object} loading相关方法
 */
export function useLoading() {
  const loading = ref(false);
  let currentInstance = null;

  /**
   * 显示loading
   * @param {Object} options - loading配置选项
   * @returns {Object} loading实例
   */
  const showLoading = (options = {}) => {
    if (currentInstance) {
      hideLoading();
    }

    loading.value = true;
    currentInstance = createLoading(options);
    return currentInstance;
  };

  /**
   * 隐藏当前loading
   */
  const hideLoading = () => {
    if (currentInstance) {
      currentInstance.close();
      currentInstance = null;
    }
    loading.value = false;
  };

  /**
   * 切换loading状态
   * @param {Object} options - loading配置选项
   * @returns {Object} loading实例或null
   */
  const toggleLoading = (options = {}) => {
    if (loading.value) {
      hideLoading();
      return null;
    } else {
      return showLoading(options);
    }
  };

  /**
   * 异步操作包装器，自动显示/隐藏loading
   * @param {Function} asyncFn - 异步函数
   * @param {Object} options - loading配置选项
   * @returns {Promise} 异步函数的结果
   */
  const withLoading = async (asyncFn, options = {}) => {
    let instance = null;
    try {
      instance = showLoading(options);
      return await asyncFn();
    } catch (error) {
      throw error;
    } finally {
      if (instance) {
        instance.close();
      }
      loading.value = false;
      currentInstance = null;
    }
  };

  return {
    loading,
    showLoading,
    hideLoading,
    toggleLoading,
    withLoading,
    destroyAll,
  };
}

// 导出单例方法，用于在非组件中使用
export const globalLoading = {
  currentInstance: null,

  show: (options = {}) => {
    if (globalLoading.currentInstance) {
      globalLoading.hide();
    }
    globalLoading.currentInstance = createLoading(options);
    return globalLoading.currentInstance;
  },

  hide: () => {
    if (globalLoading.currentInstance) {
      globalLoading.currentInstance.close();
      globalLoading.currentInstance = null;
    }
  },

  withLoading: async (asyncFn, options = {}) => {
    try {
      globalLoading.show(options);
      return await asyncFn();
    } catch (error) {
      throw error;
    } finally {
      globalLoading.hide();
    }
  },

  destroyAll: destroyAll,
};

export default {
  useLoading,
  globalLoading,
  createLoading,
};
