import { useUserStore } from '@/store/modules/userStore';

export function getMdInfo() {
  const userStore = useUserStore();
  let pageParams = userStore?.pageParams || {};
  let saveCaseData = userStore?.saveCaseData || {};
  let designerInfo = pageParams?.designerInfo;
  // 地址信息
  let customerInfo = pageParams?.customerInfo || {};

  const isEdit = userStore?.caseCode;
  if (isEdit) {
    return {
      mdCode: saveCaseData?.mdCode,
      mdName: saveCaseData?.mdName,
      provinceCode: saveCaseData?.provinceCode,
      cityCode: saveCaseData?.cityCode,
      cityName: saveCaseData?.cityName,
      village: saveCaseData?.commName,
    };
  }
  return {
    mdCode: designerInfo?.mdCode,
    mdName: designerInfo?.mdName,
    provinceCode: customerInfo?.provinceCode,
    cityCode: customerInfo?.cityCode,
    cityName: customerInfo?.cityName,
    village: customerInfo?.village,
  };
}
