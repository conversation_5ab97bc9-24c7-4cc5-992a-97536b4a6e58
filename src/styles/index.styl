//@import './icon.styl'

:root
  --color-page-background #f5f6f9
  --opn-color-required #ff2121
  --opn-color-primary: #be965a
  --opn-border-color: #ececec
  --opn--primary-color: #bf8630
  --opn--hover-color: #CCA054
  --case-item-border-color: rgba(0, 0, 0, 0.15)
  --d9-color: #d9d9d9
  --color-00085: rgba(0, 0, 0, 0.85)
  --opn-text-main: rgba(0, 0, 0, 0.85)
  --black-theme-color: rgba(0,0,0,1)
  --blue-1: #FFFBF0;
  --blue-2: #F2EEE4;
  --blue-3: #E6E1D8;
  --blue-4: #D9CAAB;
  --blue-5: #CCB081;
  --blue-66: #BF8630;
  --blue-7: #997340;
  --blue-8: #73512A;
  --blue-9: #4D3218;
  --blue-10: #26180C;
  --blue-6: #BF8630;

  --purple-1: #f9f0ff;
  --purple-2: #efdbff;
  --purple-3: #d3adf7;
  --purple-4: #b37feb;
  --purple-5: #9254de;
  --purple-6: #722ed1;
  --purple-7: #531dab;
  --purple-8: #391085;
  --purple-9: #22075e;
  --purple-10: #120338;

  --cyan-1: #e6fffb;
  --cyan-2: #b5f5ec;
  --cyan-3: #87e8de;
  --cyan-4: #5cdbd3;
  --cyan-5: #36cfc9;
  --cyan-6: #13c2c2;
  --cyan-7: #08979c;
  --cyan-8: #006d75;
  --cyan-9: #00474f;
  --cyan-10: #002329;

  --green-1: #f6ffed;
  --green-2: #d9f7be;
  --green-3: #b7eb8f;
  --green-4: #95de64;
  --green-5: #73d13d;
  --green-6: #52c41a;
  --green-7: #389e0d;
  --green-8: #237804;
  --green-9: #135200;
  --green-10: #092b00;

  --pink-1: #fff0f6;
  --pink-2: #ffd6e7;
  --pink-3: #ffadd2;
  --pink-4: #ff85c0;
  --pink-5: #f759ab;
  --pink-6: #eb2f96;
  --pink-7: #c41d7f;
  --pink-8: #9e1068;
  --pink-9: #780650;
  --pink-10: #520339;

  --red-1: #fff1f0;
  --red-2: #ffccc7;
  --red-3: #ffa39e;
  --red-4: #ff7875;
  --red-5: #ff4d4f;
  --red-6: #f5222d;
  --red-7: #cf1322;
  --red-8: #a8071a;
  --red-9: #820014;
  --red-10: #5c0011;

  --orange-1: #fff7e6;
  --orange-2: #ffe7ba;
  --orange-3: #ffd591;
  --orange-4: #ffc069;
  --orange-5: #ffa940;
  --orange-6: #fa8c16;
  --orange-7: #d46b08;
  --orange-8: #ad4e00;
  --orange-9: #873800;
  --orange-10: #612500;

  --gold-1: #fffbe6;
  --gold-2: #fff1b8;
  --gold-3: #ffe58f;
  --gold-4: #ffd666;
  --gold-5: #ffc53d;
  --gold-6: #faad14;
  --gold-7: #d48806;
  --gold-8: #ad6800;
  --gold-9: #874d00;
  --gold-10: #613400;

  --primary-1: var(--blue-1);
  --primary-2: var(--blue-2);
  --primary-3: var(--blue-3);
  --primary-4: var(--blue-4);
  --primary-5: var(--blue-5);
  --primary-6: var(--blue-6);
  --primary-7: var(--blue-7);
  --primary-8: var(--blue-8);
  --primary-9: var(--blue-9);
  --primary-10: var(--blue-10);

  --primary-color: var(--primary-6);
  --primary-color-hover: var(--primary-5);
  --primary-color-active: var(--primary-7);
  --primary-color-outline: var(--primary-2);

  --info-color: var(--primary-color);

  --success-color: var(--green-6);
  --success-color-hover: var(--green-5);
  --success-color-active: var(--green-7);
  --success-color-outline: var(--green-2);

  --warning-color: var(--gold-6);
  --warning-color-hover: var(--gold-5);
  --warning-color-active: var(--gold-7);
  --warning-color-outline: var(--gold-2);

  --error-color: var(--red-5);
  --error-color-hover: var(--red-4);
  --error-color-active: var(--red-7);
  --error-color-outline: var(--red-2);

  --highlight-color: var(--red-5);
  --processing-color: var(--blue-6);
  --card-hover-gb-color: #f5f5f5;
  --bg-color: #fafafa;


html, body,#app
  height 100%
  overflow hidden
  margin 0
  padding 0

body
  background-color var( --color-background-2 )
  color #333
  font-family PingFang SC PingFangSC-Regular, -system-ui, -apple-system, BlinkMacSystemFont, Droid Sans, Helvetica Neue, sans-serif
  text-rendering optimizeLegibility
  -moz-osx-font-smoothing grayscale
  -webkit-font-smoothing antialiased

*, *::before, *::after
  box-sizing border-box

*
  scrollbar-width: 0 !important

*::-webkit-scrollbar
  display none
  scrollbar-width: none

#crm-pc-app
  height 100%

a, a:focus, a:hover
  color inherit
  cursor pointer
  text-decoration none


.text-hidden {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-hidden-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.text-hidden-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}


.no-scroll::-webkit-scrollbar
  display none


.empty-tip
  @extend .comment-normal
  heigth 22px
  line-height 22px
  pading-top 8px

.block-table-wrap
  padding 16px 0 24px;

.ant-btn-link
  padding 0 !important
  text-align start
  height 22px
  line-height  22px

.tooltiprwUserName
  max-width 400px
  min-height 50px
  max-height 400px
  overflow auto

.ant-table-thead th
  background #fafafa !important
  padding 16px !important
  line-height 22px
  &::before
    background rgba(0, 0, 0, 0.06) !important

.ant-table-wrapper .ant-table
  border 1px solid var(--opn-border-color)

.ant-drawer .ant-drawer-title
  font-weight 500
  height 32px
  line-height: 32px;



// 圆角版本覆写 --------------------------------------------------------

//.ant-picker, .ant-select-selector, .ant-pagination .ant-pagination-item
//  border-radius 8px !important
//
//.ant-btn-sm
//  border-radius 6px !important
//
//.ant-table-wrapper .ant-table
//  border-radius 10px !important
//
//.ant-table-wrapper .ant-table-container table>thead>tr:first-child >*:first-child
//  border-radius 10px 0 0 0 !important
//
//.ant-table-wrapper .ant-table-container table>tbody>tr:last-child >*:last-child
//  border-radius 0 10px 0 0 !important
//
//.ant-table-wrapper .ant-table-container table>tbody>tr:last-child >*:first-child
//  border-radius 0 0 0 10px !important
//
//.ant-table-wrapper .ant-table-container table>tbody>tr:last-child >*:last-child
//  border-radius 0 0 10px 0 !important
//
//.ant-tag
//  border-radius 6px !important
//.ant-modal .ant-modal-content
//  border-radius 16px !important;
//
//td.ant-table-cell
//  word-break break-all !important
//  padding 16px !important
//  height 22px
//  line-height 22px
//
//th.ant-table-cell
//  word-break break-all !important
//  height 47px
//  backgound #FAFAFA
//
//.table-striped td
//  background-color #fafafa !important
//
//.ant-select-multiple .ant-select-selection-item
//  border-radius 6px !important

// 按钮黑金主题加持
/* 新增主按钮背景色 */
.syn-gzt-btn-primary {
  background-color: #000 !important;
  color: #d9ba7c !important;
}
.ant-btn-primary {
  background-color: #000 !important;
  color: #d9ba7c !important;
}
.ant-btn-background-ghost {
  background-color: transparent !important;
  color: var(--opn--primary-color) !important;
}

/* 新增主按钮悬浮状态背景色 */
.syn-gzt-btn-primary:hover {
  background-color: #262626 !important;
}

.ant-btn-primary:hover {
  background-color: #262626 !important;
}
.ant-btn-background-ghost:hover {
  background-color: transparent !important;
}

/* 新增主按钮禁用状态样式 */
.syn-gzt-btn-primary[disabled],
.syn-gzt-btn-primary[disabled]:hover {
  background-color: #bfbfbf !important;
  color: #f0f0f0 !important;
}

.ant-btn-primary[disabled],
.ant-btn-primary[disabled]:hover {
  background-color: #bfbfbf !important;
  color: #f0f0f0 !important;
}

// Synicon图标位置调整
[class^="Synicon-"] {
  position: relative;
  top: -1px;
}


