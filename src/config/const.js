import { isTest } from '@/utils/isEnv';

export const LOCAL_STORAGE_PREFIX_KEY = 'intelligent-control-design';
export const MAIN_PLATFORM_STORAGE_KEY = isTest() ? 'DZPTGZT_test' : 'DZPTGZT_prod';
export const MAIN_ACCESS_TOKEN_KEY = 'gzt_access_token';
export const MAIN_REFRESH_TOKEN_KEY = 'gzt_refresh_access_token';
export const PLATFORM = 'custom'; // 平台标识 接口请求时需要携带
export const DEFAULT_AVATAR_URL =
  'https://sybird-oss.haier.net/resource/statics/resource/syndzpt/images/profile-picture-light.png';
export const MAIN_PLATFORM_REQUEST_URLS = [
  '/syndzpt/api/syn-base-authorization/oauth2/logout',
  '/api/syn-store-construct/admin/construct/resource/getSignedUrl',
];
export const LOGIN_URL = `${origin}/console/login`;

// 图片压缩比例设置
export const PREVIEW_IMG_COMPRESS_PARAMS = 'image/resize,m_lfit,w_700';
export const CARD_IMG_COMPRESS_PARAMS = 'image/resize,m_lfit,w_400';
export const CANVAS_IMG_COMPRESS_PARAMS = 'image/resize,m_lfit,w_240';
// 设计师类型映射
export const DESIGNER_TYPE_MAP = {
  0: '官方设计师',
  1: '店铺设计师',
  3: '共享设计师',
};
