<template>
  <div class="space-attr-panel">
    <!-- 主标题 -->
    <div class="main-title">{{ configs.title }}</div>
    <a-form ref="formRef" :model="formData" autocomplete="off" size="small" name="formPart" label-align="left">
      <a-form-item :labelCol="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="control.label" :colon="control.colon"
        v-for="(control, cIndex) in configs.controls" :key="cIndex" :rules="control.rules" :name="control.name"
        :class="['control-row', control.disabled ? 'disabled' : '']">
        <component :is="control.is" v-bind="control.props" v-model:value="formData[control.name]"
          @[control.props.eventName]="(value) => handleChange(control, value)">
          <template v-if="control.unit" #addonAfter>
            <span class="unit-span">{{ control.unit }}</span>
          </template>
        </component>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { debounce } from 'lodash';
import { isArray, isTruly } from '@/utils/isType';
import { initFormConfig, inputFormItem, selectFormItem } from '@/utils/createForm';
import { useDictStore } from '@/store/modules/dictStore';
import { HOUSE_TYPE_LIST } from '@/config/dictKeys';
const dictStore = useDictStore();
const componentMap = {
  area: 'a-input-number',
  name: 'a-input',
  roomType: 'a-select',
};
const props = defineProps({
  attrs: {
    type: Object,
    default: () => ({
      uuid: '',
      attributeList: [],
    }),
  },
});

const emit = defineEmits(['onCallback']);

// 表单引用
const formRef = ref();

// 表单数据
const formData = ref({});
// --- 模拟数据 ---

// 面板配置
const configs = ref({});

/**
 * @description 修改属性事件（带防抖）
 * @param item
 */
const handleChange = debounce(async (item) => {
  try {
    // 只校验当前修改的表单项
    await formRef.value.validateFields([item.name]);
    if (!isTruly(formData.value[item.name])) {
      return;
    }

    let payload = {
      [item.name]: formData.value[item.name],
    };

    // 如果修改的是roomType，同时更新name字段为roomType的label值
    if (item.name === 'roomType') {
      const roomTypeOptions = await dictStore.getDictStore(HOUSE_TYPE_LIST);
      const selectedOption = roomTypeOptions.find((option) => option.value === formData.value[item.name]);
      if (selectedOption) {
        formData.value.name = selectedOption.label;
        payload.name = selectedOption.label;
        await formRef.value.validateFields('name');
      }
    }

    // 校验通过后触发事件
    emit('onCallback', {
      action: 'modifySpace',
      uuid: props?.attrs.uuid,
      payload,
    });
  } catch (error) {
    console.log('表单校验失败:', error);
    // 校验失败时不触发事件
  }
}, 500); // 300ms 防抖延迟

/**
 * @description 生成formList
 * @param configs
 * @returns {*}
 */
async function createFormList(configs) {
  let obj = {};
  for (const item of configs) {
    let componentItem = {
      is: componentMap[item.key],
      label: item.label,
      name: item.key,
      key: item.key,
      unit: item.unit,
      colon: false,
      disabled: item.disabled,
      labelCol: {
        style: {
          width: '60px',
        },
      },
    };
    if (item.key === 'area') {
      Object.assign(componentItem, {
        ...inputFormItem({
          placeholder: '请输入面积',
          size: 'small',
          disabled: item.disabled,
          style: {
            width: '100px',
            borderRadius: '6px',
            fontSize: '12px',
          },
          eventName: 'blur',
        }),
      });
    }
    if (item.key === 'roomType') {
      Object.assign(componentItem, {
        ...selectFormItem({
          placeholder: '请选择空间类型',
          size: 'small',
          options: await dictStore.getDictStore(HOUSE_TYPE_LIST),
          disabled: item.disabled,
          allowClear: false,
          showSearch: false,
          style: {
            width: '100px',
            textAlign: 'start',
          },
          eventName: 'select',
        }),
      });
    }
    if (item.key === 'name') {
      Object.assign(componentItem, {
        ...inputFormItem({
          placeholder: '请输入房间名称',
          maxlength: 20,
          size: 'small',
          allowClear: false,
          style: {
            width: '100px',
            fontSize: '12px',
            borderRadius: '6px',
          },
          eventName: 'change',
        }),
        rules: [
          {
            required: true,
            message: '请输入房间名称',
            trigger: ['blur', 'change'],
          },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
            message: '仅支持汉字、数字和字母',
            trigger: ['blur', 'change'],
          }
        ],
      });
    }
    obj[item.key] = componentItem;
    formData.value[item.key] = item.value;
  }
  return obj;
}

/**
 * @description 创建form配置文件
 * @param formList
 */
async function createFormConfigAndFormData(formList) {
  let config = await createFormList(formList);
  let { formItems } = initFormConfig(config);
  configs.value.controls = formItems;
}

watch(
  () => props.attrs,
  async (val) => {
    const attributeList = val?.attributeList || [];
    configs.value.title = attributeList[0]?.title;
    const controlsList = attributeList[0]?.list;
    if (isArray(controlsList) && controlsList.length) {
      await createFormConfigAndFormData(controlsList);
    } else {
      configs.value.title = '';
      configs.value.controls = [];
    }
  },
  { immediate: true, deep: true }
);
</script>

<style scoped lang="stylus">
  .space-attr-panel
    width 240px
    display flex
    flex-direction column
    background-color #fff
    padding 20px 16px;

  .main-title
    color: var(--color-00085);
    @extends .title-small
    line-height: 22px; /* 157.143% */
    margin-bottom 12px

   .group-title
      color: var(--color-00085);
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;

  .control-row
    margin-bottom 12px

  :deep(.ant-input-number-group-addon)
    padding 5px 8px
    border-color rgba(0, 0, 0, 0.15)
    color rgba(0, 0, 0, 0.25)
    font-size 12px
    font-weight 400
    background-color #fff
    border-start-end-radius 6px
    border-end-end-radius 6px
  :deep(.ant-input-number-sm)
    border-right none
    color var(--color-00085)
    border-color rgba(0, 0, 0, 0.15)
    line-height: 22px
    background-color #fff
    border-radius 6px
  :deep(.ant-input-sm)
    height: 22px
    border-radius 6px
  .unit-span
    color rgba(0, 0, 0, 0.25)
  :deep(.ant-form-item-control) {
    flex-direction column;
    align-items  flex-end !important
  }

  :deep(.ant-input-number-disabled)
    background-color #f5f5f5 !important

  .disabled
    :deep(.ant-input-number-group-addon)
      background-color #f5f5f5 !important
      border-start-end-radius 6px
      border-end-end-radius 6px
    :deep(.ant-input-number-sm)
      background-color #f5f5f5  !important
      color rgba(0, 0, 0, 0.25) !important

  .ant-form-item {
    // min-height 24px
    :deep( .ant-form-item-label) {
      >label {
        @extends .content-small
        color: var(--color-00085)
      }
    }
  }

  :deep(.ant-form-item-explain-error)
    color var(--opn-color-required)
    font-size: 11px
    line-height 20px
    padding-bottom 1px
  
  :deep(.ant-input-affix-wrapper-status-error)
    border-color: var(--opn-color-required) !important

  :deep(.ant-form-item-label >label.ant-form-item-required)
    &::before
      // display none !important
  
  :deep(.ant-select-single.ant-select-sm .ant-select-selector)
    font-size 12px
    color var(--color-00085)
    font-weight 400
    line-height 20px
    border-radius 6px
</style>
