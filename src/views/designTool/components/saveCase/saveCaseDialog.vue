<script setup>
  import { SAVE_CASE_STATE, useGlobalModalStore } from '@/store/modules/globalModalStore';
  import { reactive, ref, onMounted } from 'vue';
  import AddressSelect from '@/components/addressSelect/addressSelect.vue';
  import ClientImg from '@/components/clientImg/clientImg.vue';
  import { useUserStore } from '@/store/modules/userStore';
  import { message } from 'ant-design-vue';

  const globalModalStore = useGlobalModalStore();
  const userStore = useUserStore();

  const props = defineProps({
    title: {
      type: String,
      default: '请保存方案',
    },
    caseId: {
      type: String,
      default: '',
    },
    img: {
      type: String,
      default: '',
    },
  });
  const open = ref(true);
  const caseInfo = ref({
    caseId: props.caseId,
    name: '',
    url: props.img,
    address: {},
    detailAddress: '',
  });
  const formModel = reactive({
    name: '',
    address: {},
    detailAddress: '',
  });
  const rules = {
    name: [
      { required: true, message: '请输入方案名称', trigger: 'blur' },
      { max: 50, message: '方案名称不能超过50个字符', trigger: 'blur' },
    ],
    address: [
      { required: true, message: '请选择方案地址', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          if (value && value.provinceCode && value.cityCode) {
            callback();
          } else {
            callback(new Error('请选择完整的省/市地址'));
          }
        },
        trigger: 'change',
      },
    ],
    detailAddress: [
      { max: 50, message: '小区名称不能超过50个字符', trigger: 'blur' },
      {
        pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s\,\.\;\:\!\?\(\)\-\_\+\=\#\%\&\*\@\~\`\'\"\<\>\/\|\\\[\]]*$/,
        message: '小区名称仅支持汉字、字母、数字和普通标点符号',
        trigger: 'blur',
      },
    ],
  };
  const fourCodes = ref([]);
  const formRef = ref(null);
  const emit = defineEmits(['confirm']);
  /**
   * @description 方案保存确认
   */
  const handleConfirm = async () => {
    console.log('formModel', formModel);
    try {
      await formRef.value.validate();
    } catch (error) {
      console.error('表单验证失败:', error);
      if (error.errorFields && error.errorFields.length > 0) {
        const firstError = error.errorFields[0]?.errors[0];
        message.warn(firstError);
      }
      return;
    }
    emit('confirm', { ...formModel, thumbnail: caseInfo.value.url });
  };

  /**
   * @description 取消方案保存
   */
  function handleCancel() {
    globalModalStore.clearStoreState(SAVE_CASE_STATE);
    open.value = false;
  }

  /**
   * @description 选择地址
   */
  function handleSelectAddress({ value, options }) {
    console.log(value, options);
    formModel.address = transformAddressData(options);
    fourCodes.value = value;
  }

  /**
   * @description 获取地址数据转换
   * @param data
   */
  function transformAddressData(data) {
    let province = data[0] || {};
    let city = data[1] || {};
    let address = {
      provinceCode: province?.region_code || '',
      cityCode: city?.region_code || '',
      provinceName: province?.region_name || '',
      cityName: city?.region_name || '',
    };
    return {
      ...address,
    };
  }
  onMounted(() => {
    // 若是使用了选择户型页面，则默认取该页面选择的地址和小区
    // 若未使用页面数据，而是本地上传底图或自由绘制，则判断是否有CRM传来的默认数据
    // 若有，使用CRM的地址小区(保留前三级)
    // 若无，则置空，需要手动填写
    const addressInfo = userStore.addressInfo; // 选择户型页面选择的地址
    const crmAddress = userStore?.pageParams?.customerInfo; // crm地址
    console.log('选择户型页面选择的地址', addressInfo);
    console.log('crm地址', crmAddress);
    if (addressInfo && Object.keys(addressInfo).length > 0) {
      const addressData = [addressInfo['0'], addressInfo['1']].filter((item) => Object.keys(item).length > 0);
      formModel.address = transformAddressData(addressData);
      fourCodes.value = addressData.map((item) => item.region_code || '').filter((code) => code);
      return;
    }
    console.log('是否有地址数据', crmAddress);
    if (crmAddress && crmAddress.provinceCode && crmAddress.cityCode && crmAddress.regionCode) {
      // 回显四级地址编码
      fourCodes.value = [crmAddress.provinceCode, crmAddress.cityCode, crmAddress.regionCode];

      // 转换地址数据并赋值给表单
      const addressData = transformAddressData([
        { region_code: crmAddress.provinceCode, region_name: crmAddress.provinceName },
        { region_code: crmAddress.cityCode, region_name: crmAddress.cityName },
        { region_code: crmAddress.regionCode, region_name: crmAddress.regionName },
      ]);
      formModel.address = addressData;
    }
  });
</script>

<template>
  <a-modal
    v-model:open="open"
    @ok="handleConfirm"
    title="保存方案"
    :maskClosable="false"
    :keyboard="false"
    width="680px"
    wrapClassName="save-case-modal"
    :style="{ borderRadius: '16px', boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)' }"
    @cancel="handleCancel">
    <div class="save-case-dialog-container">
      <div class="left">
        <a-form :model="formModel" ref="formRef" :rules="rules" labelAlign="right">
          <a-form-item label="方案名称:" name="name">
            <a-textarea
              v-model:value="formModel.name"
              placeholder="请输入方案名称"
              :maxlength="50"
              show-count></a-textarea>
          </a-form-item>
          <a-form-item label="方案地址:" name="address">
            <address-select
              :fourCodes="fourCodes"
              :changeOnSelect="true"
              @onChange="handleSelectAddress"
              :max="3"></address-select>
          </a-form-item>
          <a-form-item label="小区名称:" name="detailAddress" width="72px">
            <a-textarea
              v-model:value="formModel.detailAddress"
              placeholder="请输入小区名称"
              :maxlength="50"
              show-count></a-textarea>
          </a-form-item>
        </a-form>
      </div>
      <div class="right">
        <client-img :src="caseInfo.url"></client-img>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="stylus">
  .save-case-dialog-container
    display flex
    flex-direction row
    width 100%
    height 100%
    padding: 20px 24px
    align-items: center
    gap 16px
  .left
    flex: 1
    padding-top 24px

  .right
    width: 240px
    height: 240px
    padding 12px
    box-sizing: border-box

  .client-img
    width 120px
    height 120px
    border-radius 10px
    padding 10px
    background #F5F5F5
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08)

  .ant-input
    height: 40px
    border-radius: 8px
    border: 1px solid #d9d9d9

  .ant-form-item-label > label
    font-size: 14px
    color: rgba(0, 0, 0, 0.85)
    font-weight: 400

  .ant-modal-header
    padding: 0
    border-bottom: none

  .ant-modal-body
    padding: 24px

  .ant-modal-footer
    padding: 0 24px 16px 24px
    border-top: 1px solid #e8e8e8
    background: #fafafa
    margin-top 0
</style>
<style lang="stylus">
  .save-case-modal
    .ant-modal-content
      padding: 0 !important;
    .ant-modal-header
      padding: 20px
      padding-bottom 0
      margin-bottom: 0
    .ant-modal-footer
      padding: 12px 20px 20px 20px
    .ant-form-item-label
      width 80px
</style>
