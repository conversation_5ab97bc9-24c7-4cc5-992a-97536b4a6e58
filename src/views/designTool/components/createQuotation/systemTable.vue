<script setup>
import { computed } from 'vue';
import ClientImg from '@/components/clientImg/clientImg.vue';
import { formatToTwoDecimals } from '@/utils/tools';
const props = defineProps({
  systemList: {
    type: Array,
    default: () => [],
  },
  activeSubTab: {
    type: String,
    default: 'all',
  },
  serviceFee: {
    type: Number,
    default: 0,
  },
});

// 通用columns
const columns = [
  { title: '商品', dataIndex: 'productName', key: 'productName', width: 160 },
  { title: '编码', dataIndex: 'productCode', key: 'productCode', width: 120 },
  { title: '单价', dataIndex: 'unitPrice', key: 'unitPrice', width: 100, showSorterTooltip: true, ellipsis: true },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 80 },
  { title: '金额', dataIndex: 'price', key: 'price', width: 100, showSorterTooltip: true, ellipsis: true },
  { title: '备注', dataIndex: 'remark', key: 'remark', showSorterTooltip: true, ellipsis: true },
];

const filteredSystems = computed(() => {
  if (props.activeSubTab === 'all') return props.systemList || [];
  return (props.systemList || []).filter(system => system.systemId === props.activeSubTab);
});

// 转换API数据为表格数据格式
const tableData = computed(() => {
  return filteredSystems.value.map((system) => {
    // 先提取有序的 roomId 列表
    const roomIdOrder = [];
    const roomGroups = {};
    (system.productList || []).forEach((product) => {
      const roomId = product.roomId;
      if (!roomGroups[roomId]) {
        roomGroups[roomId] = {
          roomId: product.roomId,
          roomName: product.roomName,
          remark: product.remark || '',
          children: [],
        };
        roomIdOrder.push(roomId);
      }
      roomGroups[roomId].children.push({
        ...product,
        componentName: product.productName,
        componentCode: product.productCode,
      });
    });
    return {
      systemId: system.systemId,
      systemName: system.systemName,
      totalPrice: system.productPrice || 0,
      disPrice: system.discountedPrice || 0,
      // 保证 room 分组顺序和后端一致
      children: roomIdOrder.map(id => roomGroups[id]),
    };
  });
});

// 计算rowspan
const systemRowspanMap = computed(() => {
  const map = {};
  let rowIndex = 0;
  tableData.value.forEach((systemItem) => {
    let count = 0;
    systemItem.children.forEach((roomItem) => {
      count += roomItem.children.length;
    });
    if (count > 0) {
      map[rowIndex] = count;
    }
    rowIndex += count;
  });
  return map;
});
const secondRowspanMap = computed(() => {
  const map = {};
  let rowIndex = 0;
  tableData.value.forEach((systemItem) => {
    systemItem.children.forEach((roomItem) => {
      const productCount = roomItem.children.length;
      if (productCount > 0) {
        map[rowIndex] = productCount;
      }
      rowIndex += productCount;
    });
  });
  return map;
});

// 拍平数据
const flatRows = computed(() => {
  const rows = [];
  tableData.value.forEach((primaryItem, primaryIdx) => {
    primaryItem.children.forEach((secondaryItem, secondaryIdx) => {
      secondaryItem.children.forEach((product) => {
        rows.push({
          ...product,
          unitPrice: `￥${formatToTwoDecimals(product.unitPrice)}`,
          quantity: `*${product.quantity}`,
          price: `￥${formatToTwoDecimals(product.price)}`,
          totalPrice: formatToTwoDecimals(primaryItem.totalPrice),
          disPrice: formatToTwoDecimals(primaryItem.disPrice),
          showDiscount: primaryItem.totalPrice !== primaryItem.disPrice,
          remark: secondaryItem.remark,
          _primaryIndex: primaryIdx,
          _secondaryIndex: secondaryIdx,
        });
      });
    });
  });
  return rows;
});

const tableColumn = computed(() => [
  {
    title: '智慧系统',
    dataIndex: 'systemName',
    key: 'systemName',
    width: 110,
    customCell: (record, rowIndex) => {
      const rowSpan = systemRowspanMap.value[rowIndex] || 0;
      return { rowSpan };
    },
  },
  {
    title: '空间',
    dataIndex: 'roomName',
    key: 'roomName',
    width: 80,
    customCell: (record, rowIndex) => {
      const rowSpan = secondRowspanMap.value[rowIndex] || 0;
      return { rowSpan };
    },
  },
  ...columns,
]);
</script>
<template>
  <a-table
    :dataSource="flatRows"
    :columns="tableColumn"
    :pagination="false"
    :scroll="{ y: 'calc(100vh - 350px)' }"
    bordered
    :rowKey="(record) => record.componentCode || record.productCode || record.systemId"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'systemName'">
        <div class="room-name">{{ record.systemName }}</div>
        <div class="origin-price">¥{{ record.disPrice }}</div>
        <div v-if="record.showDiscount" class="discount-price">¥{{ record.totalPrice }}</div>
      </template>
      <template v-else-if="column.dataIndex === 'roomName'">
        <div class="room-name">{{ record.roomName }}</div>
      </template>
      <template v-else-if="column.dataIndex === 'productName'">
        <div class="product-record">
          <div class="product-record-img">
            <client-img :src="record.productMasterImg" :preview="false" :alt="record.productName"></client-img>
          </div>
          <div class="product-record-name">
            <div class="product-record-name-content" v-ellipsis-tooltip>{{ record.productName }}</div>
          </div>
        </div>
      </template>
    </template>
    <template #summary>
      <a-table-summary v-if="activeSubTab === 'all'">
        <a-table-summary-row>
          <a-table-summary-cell :col-span="2">服务费</a-table-summary-cell>
          <a-table-summary-cell :col-span="6">
            <div class="service-fee">
              <div class="service-fee-label">折扣仅影响商品价格，不影响服务费</div>
              <div class="service-fee-value">¥{{ formatToTwoDecimals(serviceFee) }}</div>
            </div>
          </a-table-summary-cell>
        </a-table-summary-row>
      </a-table-summary>
    </template>
  </a-table>
</template>
<style scoped lang="stylus">
.room-name {
  font-size 14px;
  font-weight 500;
  line-height 22px;
  color rgba(0, 0, 0, 0.85);
}
.origin-price {
  font-size 12px;
  font-weight 500;
  line-height 20px;
  color #FF2121;
  margin-top 8px;
}
.discount-price {
  font-size 12px;
  font-weight 500;
  line-height 20px;
  color rgba(0, 0, 0, 0.45);
  text-decoration line-through;
  margin-top 8px;
}
.service-fee {
  display flex;
  flex-direction row;
  align-items center;
  justify-content space-between;
  &-label {
    font-size 14px;
    line-height 22px;
    color rgba(0, 0, 0, 0.85);
  }
  &-value {
    font-size 14px;
    font-weight 500;
    line-height 20px;
    color #FF2121;
  }
}
.product-record {
  display flex;
  flex-direction row;
  align-items center;
  cursor pointer;
  &-img {
    flex-shrink 0;
    width 22px !important;
    height 22px !important;
  }
  &-name {
    flex 1;
    padding 0 8px;
    overflow: hidden;
    :deep(div:first-child) {
      display block !important;
    }
    :deep(.product-record-name-content) {
      font-size 14px;
      line-height 22px;
      font-weight 400;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color rgba(0, 0, 0, 0.85);
    }
  }
}
</style>
