<script setup>
  import { ref, computed } from 'vue';
  import ClientImg from '@/components/clientImg/clientImg.vue';
  import { CREATE_QUOTATION_STATE, useGlobalModalStore } from '@/store/modules/globalModalStore';
  import { _API_getQuotePreview, _API_quoteSave } from '@/api/intelligentApi';
  import { message } from '@syn/ant-design4-vue3';
  import { useUserStore } from '@/store/modules/userStore';

  const globalModalStore = useGlobalModalStore();
  const userStore = useUserStore();
  const open = ref(true);
  const loading = ref(true);
  const confirmLoading = ref(false);
  const props = defineProps({
    title: {
      type: String,
      default: '报价单',
    },
    code: {
      type: String,
      default: '',
    },
  });
  const tabs = ref([
    {
      label: '按空间',
      value: 'space',
    },
    {
      label: '按智慧系统',
      value: 'system',
    },
  ]);
  const activeMainTab = ref('space');
  const activeSubTab = ref('all');

  // 缓存好原始数据
  const originData = ref({
    roomList: [],
    systemList: [],
  });
  const spaces = ref([]);
  const systems = ref([]);


  const columns = [
    {
      title: '商品',
      dataIndex: 'productName',
      key: 'productName',
      width: 160,
    },
    {
      title: '编码',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 120,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      showSorterTooltip: true,
      ellipsis: true,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
    },
    {
      title: '金额',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      showSorterTooltip: true,
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      showSorterTooltip: true,
      ellipsis: true,
    },
  ];

  const tableColumn = computed(() => {
    return activeMainTab.value === 'space'
      ? [
          {
            title: '空间',
            dataIndex: 'roomName',
            key: 'roomName',
            width: 110,
            customCell: (record, rowIndex) => {
              const rowSpan = spaceRowspanMap.value[rowIndex] || 0;
              console.log('空间', rowSpan);
              return { rowSpan };
            },
          },
          {
            title: '智慧系统',
            dataIndex: 'systemName',
            key: 'systemName',
            width: 100,
            customCell: (record, rowIndex) => {
              const rowSpan = secondRowspanMap.value[rowIndex] || 0;
              console.log('智慧系统', rowSpan);
              return { rowSpan };
            },
          },
          ...columns,
        ]
      : [
          {
            title: '智慧系统',
            dataIndex: 'systemName',
            key: 'systemName',
            width: 110,
            customCell: (record, rowIndex) => {
              const rowSpan = spaceRowspanMap.value[rowIndex] || 0;
              return { rowSpan };
            },
          },
          {
            title: '空间',
            dataIndex: 'roomName',
            key: 'roomName',
            width: 80,
            customCell: (record, rowIndex) => {
              const rowSpan = secondRowspanMap.value[rowIndex] || 0;
              return { rowSpan };
            },
          },
          ...columns,
        ];
  });

  // 转换API数据为表格数据格式
  const tableData = computed(() => {
    if (!originData.value) return [];

    if (activeMainTab.value === 'space') {
      // 空间模式：空间 → 智慧系统 → 产品列表
      return (originData.value.roomList || []).map((room) => {
        // 按系统分组产品
        const systemGroups = {};
        (room.productList || []).forEach((product) => {
          const systemId = product.systemId;
          if (!systemGroups[systemId]) {
            systemGroups[systemId] = {
              systemId: product.systemId,
              systemName: product.systemName,
              remark: product.remark,
              children: [],
            };
          }
          systemGroups[systemId].children.push({
            ...product,
            // 确保字段名称一致
            componentName: product.productName,
            componentCode: product.productCode,
            price: product.unitPrice || 0,
            quantity: product.quantity || 1,
            amount: (product.unitPrice || 0) * (product.quantity || 1),
          });
        });

        return {
          roomId: room.roomId,
          roomName: room.roomName,
          totalPrice: room.productPrice || 0,
          disPrice: room.discountedPrice,
          children: Object.values(systemGroups),
        };
      });
    } else {
      // 系统模式：智慧系统 → 空间 → 产品列表
      return (originData.value.systemList || []).map((system) => {
        // 按房间分组产品
        const roomGroups = {};
        (system.productList || []).forEach((product) => {
          const roomId = product.roomId;
          if (!roomGroups[roomId]) {
            roomGroups[roomId] = {
              roomId: product.roomId,
              roomName: product.roomName,
              remark: product.remark || '',
              children: [],
            };
          }
          roomGroups[roomId].children.push({
            ...product,
            // 确保字段名称一致
            componentName: product.productName,
            componentCode: product.productCode,
            price: product.unitPrice || 0,
            quantity: product.quantity || 1,
            amount: (product.unitPrice || 0) * (product.quantity || 1),
          });
        });

        return {
          systemId: system.systemId,
          systemName: system.systemName,
          totalPrice: system.productPrice || 0,
          disPrice: system.discountedPrice || 0,
          children: Object.values(roomGroups),
        };
      });
    }
  });

  const subTabs = computed(() => {
    // 根据类型处理二级分类
    return activeMainTab.value === 'space' ? spaces.value : systems.value;
  });
  // 监听主标签点击后，表格数据变化
  const filteredData = computed(() => {
    if (activeMainTab.value === 'space') {
      if (activeSubTab.value === 'all') return tableData.value;
      return tableData.value.filter((item) => item.roomId === activeSubTab.value);
    } else {
      if (activeSubTab.value === 'all') return tableData.value;
      return tableData.value.filter((item) => item.systemId === activeSubTab.value);
    }
  });

  // 拍平数据，记录空间、系统、商品的索引
  const flatRows = computed(() => {
    const rows = [];
    filteredData.value.forEach((primaryItem, primaryIdx) => {
      primaryItem.children.forEach((secondaryItem, secondaryIdx) => {
        secondaryItem.children.forEach((product) => {
          rows.push({
            ...product,
            unitPrice: `￥${product.unitPrice}`,
            quantity: `*${product.quantity}`,
            price: `￥${product.price}`,
            // 保持原有字段用于价格显示
            totalPrice: primaryItem.totalPrice,
            disPrice: primaryItem.disPrice,
            showDiscount: primaryItem.totalPrice !== primaryItem.disPrice,
            remark: secondaryItem.remark,
            // 索引信息
            _primaryIndex: primaryIdx,
            _secondaryIndex: secondaryIdx,
          });
        });
      });
    });
    return rows;
  });

  // 计算每个空间、分类的起始行和rowspan
  const spaceRowspanMap = computed(() => {
    const map = {};
    let rowIndex = 0;
    filteredData.value.forEach((spaceItem) => {
      let count = 0;
      spaceItem.children.forEach((catItem) => {
        count += catItem.children.length;
      });
      if (count > 0) {
        map[rowIndex] = count;
      }
      rowIndex += count;
    });
    return map;
  });

  // 计算每个分类、备注的起始行和rowspan
  const secondRowspanMap = computed(() => {
    const map = {};
    let rowIndex = 0;
    filteredData.value.forEach((spaceItem) => {
      spaceItem.children.forEach((catItem) => {
        const productCount = catItem.children.length;
        if (productCount > 0) {
          map[rowIndex] = productCount;
        }
        rowIndex += productCount;
      });
    });
    return map;
  });

  function handleOk() {
    confirmLoading.value = true;
    const { customerInfo = {} } = userStore.pageParams;
    _API_quoteSave({
      caseCode: props.code,
      designerShopCode: customerInfo.mdCode,
      designerShopName: customerInfo.mdName,
    })
      .then((res) => {
        if (res['code'] === '0') {
          message.success('保存成功!');
          handleCancel();
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  function handleCancel() {
    globalModalStore.clearStoreState(CREATE_QUOTATION_STATE);
  }

  function handleTabChange() {
    activeSubTab.value = 'all';
  }

  function generateTabData(arr, label, value) {
    const $arr = arr.map((item) => ({
      label: item[label],
      value: item[value],
    }));
    return [
      {
        label: '全部',
        value: 'all',
      },
      ...$arr,
    ];
  }

  async function getData() {
    // ZK_250804000223
    console.log('code', props.code);
    loading.value = true;
    try {
      const { code, data } = await _API_getQuotePreview(props.code);
      if (code === '0') {
        originData.value = data;
        // 有数据后先初始化两个二级分类的数据
        spaces.value = generateTabData(data.roomList || [], 'roomName', 'roomId');
        systems.value = generateTabData(data.systemList || [], 'systemName', 'systemId');
      }
    } finally {
      loading.value = false;
    }

  }

  getData();
</script>

<template>
  <a-modal
    v-model:open="open"
    :title="props.title"
    :maskClosable="false"
    :keyboard="false"
    destroy-on-close
    :width="970"
    centered
    wrapClassName="create-quotation-modal"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel">
    <div class="header">
      <div class="header-body">
        <a-segmented block v-model:value="activeMainTab" :options="tabs" size="large" @change="handleTabChange" />
      </div>
    </div>
    <a-tabs v-model:activeKey="activeSubTab" :loading="loading">
      <a-tab-pane v-for="tab in subTabs" :key="tab.value" :tab="tab.label" />
    </a-tabs>
    <a-table
      :dataSource="flatRows"
      :columns="tableColumn"
      :pagination="false"
      :scroll="{ y: 'calc(100vh - 350px)' }"
      :loading="loading"
      :rowKey="(record) => record.componentCode || record.productCode || record.roomId || record.systemId"
      bordered>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'roomName'">
          <div class="room-name">{{ record.roomName }}</div>
          <template v-if="activeMainTab === 'space'">
            <div class="origin-price">¥{{ record.totalPrice }}</div>
            <div v-if="record.showDiscount" class="discount-price">¥{{ record.disPrice }}</div>
          </template>
        </template>
        <template v-else-if="column.dataIndex === 'systemName'">
          <div class="room-name">{{ record.systemName }}</div>
          <template v-if="activeMainTab === 'system'">
            <div class="origin-price">¥{{ record.totalPrice }}</div>
            <div v-if="record.showDiscount" class="discount-price">¥{{ record.disPrice }}</div>
          </template>
        </template>
        <template v-else-if="column.dataIndex === 'productName'">
          <div class="product-record">
            <div class="product-record-img">
              <client-img :src="record.productMasterImg" :preview="false" :alt="record.productName"></client-img>
            </div>
            <div class="product-record-name">
              <div class="product-record-name-content" v-ellipsis-tooltip>{{ record.productName }}</div>
            </div>
          </div>
        </template>
      </template>
    </a-table>
    <template #footer>
      <div class="footer">
        <div class="footer-left">
           <span class="title">总价:</span>
          <div class="content">
            <div class="content-price">￥{{ originData.productPrice || "0.00" }}</div>
            <div v-if="originData.productPrice !== originData.discountedPrice" class="content-discount">￥{{ originData.discountedPrice || "0.00" }}</div>
          </div>
        </div>
        <div class="footer-right">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleOk" :loading="confirmLoading">保存</a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>

<style scoped lang="stylus">
  .header {
    display flex;
    align-items center;
    justify-content center;
    padding-bottom 12px;

    &-body {
      width 268px;
    }
  }

  .product-record {
    display flex;
    flex-direction row;
    align-items center;
    cursor pointer;

    &-img {
      flex-shrink 0
      width 22px !important;
      height 22px !important;
    }

    &-name {
      flex 1
      padding 0 8px;
      overflow: hidden;

      :deep(div:first-child) {
        display block !important;
      }

      :deep(.product-record-name-content) {
        font-size 14px;
        line-height 22px;
        font-weight 400;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color rgba(0, 0, 0, 0.85);
      }
    }
  }
    
  :deep(.ant-table-thead th) {
    background-color #FFFBF0!important;
  }

  .room-name {
    font-size 14px;
    font-weight 500;
    line-height 22px;
    color rgba(0, 0, 0, 0.85);
  }

  .origin-price {
    font-size 12px;
    font-weight 500;
    line-height 20px;
    color #FF2121;
    margin-top 8px;
  }
  .discount-price {
    font-size 12px;
    font-weight 500;
    line-height 20px;
    color rgba(0, 0, 0, 0.45);
    text-decoration line-through;
    margin-top 8px;
  }
    .footer {
      display flex;
      flex-direction row;
      align-items center;
      justify-content space-between;
      &-left {
        display flex;
        flex-direction row;
        align-items center;
        .title {
          font-size 14px;
          font-weight 400;
          line-height 22px;
          color rgba(0, 0, 0, 0.85);
        }
        .content {
          display flex;
          flex-direction row;
          justify-content center;
          align-items flex-end;
          padding-left 12px;
          &-price {
            font-size 20px;
            font-weight 500;
            line-height 28px;
            color #FF2121;
          }
          &-discount {
            font-size 14px;
            font-weight 400;
            line-height 22px;
            color rgba(0, 0, 0, 0.45);
            text-decoration line-through;
            margin-left 8px;
          }
        }
      }
      &-right {
        display flex;
        flex-direction row;
        align-items center;
      }
    }
</style>
<style lang="stylus">
  // 全局样式，用于控制 modal 高度
  .create-quotation-modal
    .ant-modal-content
      display flex
      flex-direction column
      height calc(100vh - 64px) !important
    .ant-modal-body
      display flex
      flex-direction column
      flex 1
</style>
