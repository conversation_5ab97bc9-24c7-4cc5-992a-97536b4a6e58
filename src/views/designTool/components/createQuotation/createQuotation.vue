<script setup>
import { ref, computed } from 'vue';
import { CREATE_QUOTATION_STATE, SET_INTELLIGENT_SCENE_STATE, useGlobalModalStore } from '@/store/modules/globalModalStore';
import { _API_getQuotePreview, _API_quoteSave } from '@/api/intelligentApi';
import { message } from '@syn/ant-design4-vue3';
import { getMdInfo } from '@/hooks/useMdInfo';
import SpaceTable from './spaceTable.vue';
import SystemTable from './systemTable.vue';
import { formatToTwoDecimals } from '@/utils/tools';

const globalModalStore = useGlobalModalStore();
const open = ref(true);
const loading = ref(true);
const confirmLoading = ref(false);
const props = defineProps({
  title: {
    type: String,
    default: '报价单',
  },
  code: {
    type: String,
    default: '',
  },
});
const tabs = ref([
  { label: '按空间', value: 'space' },
  { label: '按智慧系统', value: 'system' },
]);
const activeMainTab = ref('space');
const activeSubTab = ref('all');

// 原始数据和二级tab数据
const originData = ref({ roomList: [], systemList: [] });
const spaces = ref([]);
const systems = ref([]);

const subTabs = computed(() => {
  return activeMainTab.value === 'space' ? spaces.value : systems.value;
});

function handleOk() {
  confirmLoading.value = true;
  let mdInfo = getMdInfo();
  _API_quoteSave({
    caseCode: props.code,
    designerShopCode: mdInfo.mdCode,
    designerShopName: mdInfo.mdName,
  })
    .then((res) => {
      if (res['code'] === '0') {
        message.success('保存成功!');
        globalModalStore.clearStoreState(SET_INTELLIGENT_SCENE_STATE);
        handleCancel();
      }
    })
    .finally(() => {
      confirmLoading.value = false;
    });
}

function handleCancel() {
  globalModalStore.clearStoreState(CREATE_QUOTATION_STATE);
}

function handleTabChange() {
  activeSubTab.value = 'all';
}

function generateTabData(arr, label, value) {
  const $arr = arr.map((item) => ({
    label: item[label],
    value: item[value],
  }));
  return [
    { label: '全部', value: 'all' },
    ...$arr,
  ];
}

async function getData() {
  loading.value = true;
  try {
    const { code, data } = await _API_getQuotePreview(props.code);
    if (code === '0') {
      originData.value = data;
      spaces.value = generateTabData(data.roomList || [], 'roomName', 'roomId');
      systems.value = generateTabData(data.systemList || [], 'systemName', 'systemId');
    }
  } finally {
    loading.value = false;
  }
}
getData();
</script>

<template>
  <a-modal
    v-model:open="open"
    :title="props.title"
    :maskClosable="false"
    :keyboard="false"
    destroy-on-close
    :width="970"
    centered
    wrapClassName="create-quotation-modal"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel">
    <div class="header">
      <div class="header-body">
        <a-segmented block v-model:value="activeMainTab" :options="tabs" size="large" @change="handleTabChange" />
      </div>
    </div>
    <a-tabs v-model:activeKey="activeSubTab" :loading="loading">
      <a-tab-pane v-for="tab in subTabs" :key="tab.value" :tab="tab.label" />
    </a-tabs>
    <SpaceTable v-if="activeMainTab === 'space'" :roomList="originData.roomList" :serviceFee="originData.serviceFee" :activeSubTab="activeSubTab" />
    <SystemTable v-else :systemList="originData.systemList" :serviceFee="originData.serviceFee" :activeSubTab="activeSubTab" />
    <template #footer>
      <div class="footer">
        <div class="footer-left">
          <span class="title">总价:</span>
          <div class="content">
            <div class="content-price">￥{{ formatToTwoDecimals(originData.discountedTotalPrice || '0.00') }}</div>
            <div v-if="originData.totalPrice !== originData.discountedTotalPrice" class="content-discount">
              ￥{{ formatToTwoDecimals(originData.totalPrice || '0.00') }}
            </div>
          </div>
        </div>
        <div class="footer-right">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleOk" :loading="confirmLoading">保存</a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>

<style scoped lang="stylus">
:deep(.ant-table-thead th) {
  background-color #FFFBF0 !important;
}
:deep(.ant-table-cell) {
  padding 12px !important;
}
.header {
  display flex;
  align-items center;
  justify-content center;
  padding-bottom 12px;
  &-body {
    width 268px;
  }
}
.footer {
  display flex;
  flex-direction row;
  align-items center;
  justify-content space-between;
  &-left {
    display flex;
    flex-direction row;
    align-items center;
    .title {
      font-size 14px;
      font-weight 400;
      line-height 22px;
      color rgba(0, 0, 0, 0.85);
    }
    .content {
      display flex;
      flex-direction row;
      justify-content center;
      align-items flex-end;
      padding-left 12px;
      &-price {
        font-size 20px;
        font-weight 500;
        line-height 28px;
        color #FF2121;
      }
      &-discount {
        font-size 14px;
        font-weight 400;
        line-height 22px;
        color rgba(0, 0, 0, 0.45);
        text-decoration line-through;
        margin-left 8px;
      }
    }
  }
  &-right {
    display flex;
    flex-direction row;
    align-items center;
  }
}
</style>
<style lang="stylus">
// 全局样式，用于控制 modal 高度
.create-quotation-modal
  .ant-modal-content
    display flex
    flex-direction column
    height calc(100vh - 64px) !important
  .ant-modal-body
    display flex
    flex-direction column
    flex 1
</style>
