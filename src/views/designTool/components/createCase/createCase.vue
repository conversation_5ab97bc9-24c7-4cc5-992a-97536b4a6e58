<script setup>
  import {
    CREATE_CASE_STATE,
    IMPORT_BASE_MAP,
    useGlobalModalStore,
  } from '@/store/modules/globalModalStore';
  import freeDraw from '@/assets/images/free-draw.png';
  import importBaseMap from '@/assets/images/import.png';
  import recognizeHouse from '@/assets/images/recognize-house.png';

  import { nextTick, ref, unref } from 'vue';
  const globalModalStore = useGlobalModalStore();
  const open = ref(true);
  const selectedOption = ref(null); // 添加选中状态管理

  const actionEnum = unref({
    importBaseMap: IMPORT_BASE_MAP,
    recognizeHouse: IMPORT_BASE_MAP,
  });

  const createCaseList = ref([
    {
      label: '自由绘制',
      action: 'freeDraw',
      icon: freeDraw,
      gioKey: 'CZHT30620',
    },
    {
      label: '导入底图',
      action: 'importBaseMap',
      icon: importBaseMap,
    },
    {
      label: '户型识别',
      action: 'recognizeHouse',
      icon: recognizeHouse,
    },
  ]);

  // 处理选项点击
  function handleOptionClick(item) {
    selectedOption.value = item.action;
  }

  // 处理确认按钮
  function handleConfirm() {
    if (!selectedOption.value) {
      return;
    }

    // 应该先关闭当前弹窗再处理其他的操作
    handleClose();
    nextTick(() => {
      const action = actionEnum[selectedOption.value];
      console.log('action', action);
      action && globalModalStore.setStoreState(action, {
        show: true,
        props: {
          title: selectedOption.label,
          type: selectedOption.value,
        },
      });
    });
  }

  // 处理关闭按钮
  function handleClose() {
    globalModalStore.clearStoreState(CREATE_CASE_STATE);
  }
</script>

<template>
  <a-modal
    v-model:open="open"
    title="新建方案"
    :maskClosable="false"
    :keyboard="false"
    width="640px"
    :closable="true"
    wrapClassName="create-case-modal"
    @cancel="handleClose"
    @ok="handleConfirm"
    :okButtonProps="{ disabled: !selectedOption }">
    <div class="create-case-dialog-container">
      <!-- 主要内容区域 -->
      <div class="create-case-content">
        <div class="create-case-list">
          <div
            v-for="(item, index) in createCaseList"
            :key="index"
            class="create-case-item"
            :class="{ 'create-case-item--selected': selectedOption === item.action }"
            @click="handleOptionClick(item)">
            <div class="create-case-item-icon">
              <img :src="item.icon" :alt="item.label" />
            </div>
            <span class="create-case-item-label">{{ item.label }}</span>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="stylus">
  .create-case-dialog-container
    display flex
    flex-direction column
    justify-content center
    align-items center
    width 100%
    flex 1
    padding 20px

  .create-case-content
    flex 1
    display flex
    justify-content center
    align-items center

  .create-case-list
    display flex
    flex-direction row
    justify-content center
    align-items center
    gap 32px
    width 100%

  .create-case-item
    display flex
    flex-direction column
    justify-content center
    align-items center
    gap 12px
    padding 12px 20px
    background-color #FFFFFF
    border 1px solid #BFBFBF
    border-radius 8px
    cursor pointer
    transition all 0.3s ease
    width 140px
    position relative

    &:hover:not(&--selected)
      background-color #FAFAFA
      border-color #D9D9D9
      transform translateY(-2px)
      box-shadow 0px 4px 12px rgba(0, 0, 0, 0.1)

    &--selected
      background-color #FFFBF0
      border-color #BF8630
      box-shadow 0px 0px 0px 2px rgba(255, 251, 240, 1)

      &:hover
        background-color #FFF8E6
        box-shadow 0px 0px 0px 2px rgba(255, 251, 240, 1), 0px 4px 12px rgba(191, 134, 48, 0.2)

  .create-case-item-icon
    width 100px
    height 100px
    display flex
    align-items center
    justify-content center
    transition transform 0.3s ease

    img
      width 100%
      height 100%
      object-fit contain
      transition all 0.3s ease

  .create-case-item:hover .create-case-item-icon
    transform scale(1.05)

    img
      filter brightness(1.1)

  .create-case-item-label
    font-family 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif
    font-weight 400
    font-size 16px
    line-height 24px
    color rgba(0, 0, 0, 0.85)
    text-align center
    white-space nowrap
    transition color 0.3s ease

  .create-case-item:hover:not(.create-case-item--selected) .create-case-item-label
    color rgba(0, 0, 0, 0.95)

  .create-case-item--selected .create-case-item-label
    color #BF8630
    font-weight 500
</style>

<style lang="stylus">
  // 全局样式，用于控制 modal 高度
  .create-case-modal
    .ant-modal-content
      display flex
      flex-direction column
      height 420px !important
    .ant-modal-body
      display flex
      flex-direction column
      flex 1
</style>
