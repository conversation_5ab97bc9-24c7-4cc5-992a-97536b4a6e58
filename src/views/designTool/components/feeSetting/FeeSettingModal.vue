<template>
  <a-modal
    v-model:open="open"
    :title="props.type === 'serviceFee' ? '服务费设置' : '折扣设置'"
    :footer="null"
    width="400px"
    wrapClassName="fee-setting-modal"
    @cancel="handleCancel">
    <div class="fee-setting-content">
      <a-form :model="formModel" :rules="rules" labelAlign="right">
        <a-form-item name="discountRate" :label="props.type === 'serviceFee' ? '服务费比例:' : '折扣比例:'">
          <a-input v-model:value="formModel.discountRate" placeholder="请输入比例" class="input">
            <template #addonAfter>%</template>
          </a-input>
        </a-form-item>
      </a-form>

      <div class="button-group">
        <a-button @click="handleCancel" class="cancel-btn">取消</a-button>
        <a-button type="primary" @click="handleConfirm" class="confirm-btn">确认</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, defineProps, defineEmits, watch } from 'vue';
  import { _API_saveServiceFeeRate, _API_saveProductDiscountRate } from '@/api/userInfo.js';
  import { message } from 'ant-design-vue';
  import { useUserStore } from '@/store/modules/userStore';

  const userStore = useUserStore();
  const props = defineProps({
    rate: {
      type: Number,
      required: true,
    },
    type: {
      type: String,
      required: true,
      validator: (value) => ['discount', 'serviceFee'].includes(value),
    },
  });
  const open = ref(true);
  const emit = defineEmits(['cancel']);
  const formModel = ref({
    discountRate: '',
  });
  const rules = {
    discountRate: [
      {
        required: true,
        trigger: 'blur',
        validator: (rule, value, callback) => {
          console.log('value', value);
          const numRegex = /^(100|[1-9]?\d)$/;
          if (!numRegex.test(value)) {
            callback(new Error('请输入0～100的整数'));
          } else {
            callback();
          }
        },
      },
    ],
  };
  // 监听父组件 rate 变化，更新 discountRate
  watch(
    () => props.rate,
    (newRate) => {
      formModel.value.discountRate = newRate + '' || 0;
    },
    {
      immediate: true,
      deep: true,
    }
  );

  const handleCancel = () => {
    emit('cancel');
  };

  const handleConfirm = async () => {
    try {
      if (formModel.value.discountRate === props.rate) {
        emit('cancel');
        return;
      }
      const saveParams =
        props.type === 'serviceFee'
          ? { zkServiceFeeRate: formModel.value.discountRate }
          : { productDiscountRate: formModel.value.discountRate, code: userStore.saveCaseData?.code };
      const res = await (props.type === 'serviceFee' ? _API_saveServiceFeeRate : _API_saveProductDiscountRate)(
        saveParams
      );
      if (res?.code === '0') {
        message.success('折扣设置成功！');
        emit('success');
      }
    } catch (error) {
      message.warning('折扣设置失败！');
      console.error('Failed to save service fee rate:', error);
    }
  };
</script>

<style scoped lang="stylus">
  .fee-setting-content
    padding-top 20px
    background #ffffff

  .input
    width 180px

  .button-group
    display flex
    justify-content flex-end
    gap 16px
    padding-top 12px

  .cancel-btn
    height 32px
    border-radius 6px
    font-size 14px
    font-weight 500
    padding 0 16px
    border-radius: 8px

  .confirm-btn
    height 32px
    border-radius 6px
    font-size 14px
    font-weight 500
    padding 0 16px
    border-radius: 8px
</style>
<style lang="stylus">
  .fee-setting-modal
    .ant-modal-header
      margin 0
    .ant-input-wrapper
      border-radius: 8px
      border: 1px solid transparent
      &:hover
        border: 1px solid #BF8630
    .ant-input-group-addon
      background: transparent
      color rgba(0, 0, 0, 0.25)
      font-family "PingFang SC"
      font-size 14px
      font-style normal
      font-weight 400
      line-height 22px
    .ant-input
      border-right: none;
      &:hover
        border-color: #d9d9d9
</style>
