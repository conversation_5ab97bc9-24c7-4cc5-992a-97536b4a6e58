import { computed } from 'vue';
import { useDeviceAttributesStore } from '@/store/modules/deviceAttributes';

export const deviceToolsBtn = () => {
  const deviceAttributesStore = useDeviceAttributesStore();
  const selectDevice = computed(() => {
    return deviceAttributesStore.attributeInfo?.selectType === 'Device';
  });
  // const canDelete = computed(() => {
  //   let canDeleteType = ['Device', ''];
  //   return Object.keys(deviceAttributesStore.attributeInfo.attribute).length > 0;
  // });
  return [
    {
      label: '复制',
      action: 'copyDevice',
      icon: 'synCopy',
      disabled: !selectDevice.value,
    },
    {
      label: '删除',
      action: 'deleteDevice',
      icon: 'synDelete',
      disabled: !selectDevice.value,
    },
    {
      label: '智慧场景',
      action: 'intelligentScene',
      icon: 'synSmartScene',
    },
  ];
};
