export const HEADER_BUTTONS = () => {
  return [
    {
      label: '保存',
      action: 'save',
      icon: 'synSave2',
      disabled: false,
    },
    {
      label: '撤销',
      action: 'cancel',
      icon: 'synUndo',
      disabled: false,
      status: 'canUndo',
      debounce: 300,
    },
    {
      label: '恢复',
      action: 'redo',
      icon: 'synRecover',
      disabled: false,
      status: 'canRedo',
      debounce: 300,
    },
    {
      label: '清空',
      action: 'clear',
      icon: 'synClearUp',
      disabled: false,
    },
    {
      label: '',
      action: null,
      gap: true,
    },
    {
      label: '清单报价',
      action: 'quote',
      icon: 'synProposalQuote',
      disabled: false,
      gioKey: 'CZHT30625',
    },
    {
      label: '生成施工图',
      action: 'generateWorkDrawing',
      icon: 'synGenerateConstructionDrawing',
      disabled: false,
      gioKey: 'CZHT30626',
    },
    {
      label: '生成提案',
      action: 'generateProposal',
      icon: 'synGenerateProposal',
      disabled: false,
      gioKey: 'CZHT30627',
    },
  ];
};
