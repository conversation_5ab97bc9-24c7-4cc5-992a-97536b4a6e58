<template>
  <Transition name="fade" mode="out-in">
    <page-layout customClass="working-drawing-page">
      <template #header>
        <div class="header-content">
          <h2 class="page-title">施工图预览</h2>
          <div class="header-buttons">
            <a-button class="header-button" size="small" @click="close">关闭</a-button>
          </div>
        </div>
      </template>
      <template #container>
        <div class="container-content" ref="containerRef" @scroll="onScroll">
          <div class="anchor-header">
            <div class="anchor-list">
              <div
                v-for="item in anchor"
                :key="item.key"
                :class="['anchor-item', { active: activeAnchor === item.key }]"
                @click="scrollToSection(item.key)">
                {{ item.title }}系统
              </div>
            </div>
          </div>
          <div class="content">
            <a-image-preview-group>
              <div v-for="item in anchor" :key="item.key" :id="`section-${item.key}`" class="section" ref="sectionRefs">
                <!-- <h3>{{ item.title }}</h3> -->
                <!-- 可插入具体内容 -->
                <client-img :src="item.img" ignoreCompress="false" :fallback="fallbackImg" />
              </div>
            </a-image-preview-group>
          </div>
        </div>
      </template>
    </page-layout>
  </Transition>
</template>
<script setup>
  import PageLayout from '@/layouts/pageLayout.vue';
  import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue';
  import { useGlobalModalStore, CREATE_WORKING_RESULT_STATE } from '@/store/modules/globalModalStore';
  import ClientImg from '@/components/clientImg/clientImg.vue';
  import { useCaseDataStore } from '@/store/modules/caseDataStore';

  const globalModalStore = useGlobalModalStore();
  const caseDataStore = useCaseDataStore();

  const anchor = ref([]);
  const activeAnchor = ref('');
  const containerRef = ref(null);
  const sectionRefs = ref([]);
  const fallbackImg = 'https://static-zjsj.haier.net/haier/file/upload/7358b793b8e843a8b0449df061d428da.png';

  const close = () => {
    globalModalStore.setStoreState(CREATE_WORKING_RESULT_STATE, {
      show: false,
    });
  };

  // 监听 caseDataStore.renderingImgList 变化
  watch(
    () => caseDataStore.renderingImgList,
    (newList) => {
      nextTick(() => {
        console.log('newList', newList);
        anchor.value = newList.map((item) => ({
          key: item.systemId,
          href: `#section-${item.systemId}`,
          title: item.systemName,
          img: item.img,
        }));
      });
    },
    { immediate: true, deep: true }
  );

  // 滚动时计算当前激活锚点
  const onScroll = () => {
    if (!containerRef.value) return;
    const scrollTop = containerRef.value.scrollTop;
    let current = '';
    // 获取所有 section dom
    const sections = sectionRefs.value;
    for (let i = 0; i < sections.length; i++) {
      const el = sections[i];
      if (el) {
        const offset = el.offsetTop - 160; // 80为padding-top
        if (scrollTop >= offset) {
          current = anchor.value[i]?.key;
        }
      }
    }
    activeAnchor.value = current || (anchor.value[0] && anchor.value[0].key);
  };

  // 点击锚点滚动到对应 section
  const scrollToSection = (key) => {
    nextTick(() => {
      const section = sectionRefs.value.find((el, idx) => anchor.value[idx]?.key === key);
      if (section && containerRef.value) {
        const top = section.offsetTop - 160; // 可根据实际调整
        containerRef.value.scrollTo({
          top,
          behavior: 'smooth',
        });
      }
    });
  };

  onMounted(() => {
    nextTick(() => {
      // 获取所有 section dom
      sectionRefs.value = Array.from(containerRef.value.querySelectorAll('.section'));
      onScroll();
    });
    containerRef.value && containerRef.value.addEventListener('scroll', onScroll);
  });

  onUnmounted(() => {
    containerRef.value && containerRef.value.removeEventListener('scroll', onScroll);
  });
</script>

<style scoped lang="stylus">
  .working-drawing-page
    position fixed
    z-index 20
    top 0
    left 0
    width 100%
    height 100vh
    background-color #E8EEF1
    padding 0
  .header-content
    padding 16px 20px
    height: 56px
    border-bottom 1px solid #e8e8e8
    display flex
    justify-content space-between
    align-items center
    background: #fff

  .page-title
    margin 0
    font-size 18px
    color rgba(0, 0, 0, 0.85)
    font-size: 16px;
    font-weight: 500;

  .header-buttons
    display flex
    gap 8px
    .header-button
      border-radius: 8px

  .header-button
    height 32px
    padding 0 16px
  .container-content
    height: calc(100vh - 56px)
    overflow-y: auto
    margin 0 auto
    padding-top 104px
    padding-bottom 400px
  .anchor-header
    position fixed
    height: 56px
    top 56px
    left 50%
    padding: 24px 0
    transform: translateX(-50%)
    right 0
    width 1092px
    background: #E8EEF1
    box-sizing: content-box
    display: flex
    align-items: center
    z-index 1
    .anchor-list
      display flex
      width 100%
      height: 100%
      align-items center
      justify-content: start
      border-bottom 1px solid rgba(0, 0, 0, 0.15)
      padding: 0 24px
      .anchor-item
        display flex
        align-items: center
        cursor pointer
        height: 100%
        border-radius 4px
        font-size 20px
        color #666
        font-family: "PingFang SC"
        transition background 0.2s, color 0.2s
        position relative
        &.active
          color #BF8630
          font-weight 500
          &:after
            content ''
            position absolute
            bottom 0
            left 50%
            transform: translateX(-50%)
            width 30px
            height 2px
            border-radius: 2px
            background-color #BF8630
        & + .anchor-item
          margin-left 60px
  .section
    &+ .section
      margin-top 20px
</style>
<style lang="stylus">
  .working-drawing-page
    .content
      .ant-anchor-link
        opacity 0
    .ant-anchor
      padding 0 24px
      height: 56px
      align-items: center
      .ant-anchor-link
        flex:  1
        color: rgba(0, 0, 0, 0.65)
        font-family: "PingFang SC"
        font-size: 20px
        font-style: normal
        font-weight: 500
        line-height: 28px
    .section
      .ant-image
         max-width 1092px
</style>
