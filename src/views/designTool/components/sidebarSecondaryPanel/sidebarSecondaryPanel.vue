<template>
  <div ref="sidebarPanelRef" class="sidebar-secondary-panel">
    <CustomTabs :tabs="secondaryTabs" position="center" v-model:activeKey="secondaryTabType">
      <template #furniture>
        <!-- Fixed Header Section -->
        <div ref="fixedHeaderRef" v-show="!loading" class="fixed-header">
          <!-- Search Bar - Always Fixed -->
          <div class="header-search">
            <SearchBar
              v-model="searchValue"
              :loading="searchLoading"
              :is-search-mode="isSearch"
              @search="handleSearch"
              @back="handleSearchBack"
              @clear="handleSearchClear" />
          </div>

          <!-- Main Category Grid - Fixed when not searching -->
          <div v-if="!isSearch" class="header-categories">
            <CategoryGrid
              :categories="mainCategories"
              :active-key="activeMainCategoryIdx"
              :columns="3"
              @category-select="handleMainCategorySelect" />
          </div>

          <!-- Secondary Category Tabs - Fixed when not searching -->
          <div v-if="!isSearch" class="header-tabs">
            <CategoryTabs
              :categories="allCategories"
              :active-index="activeCategoryIdx"
              @tab-change="handleCategoryTabChange" />
          </div>
        </div>

        <!-- Scrollable Content Section -->
        <div ref="scrollableContentRef" class="scrollable-content" :style="{ height: scrollableHeight + 'px' }">
          <!-- Content List for Normal Mode -->
          <div v-if="!isSearch" class="content-wrapper">
            <ContentList
              ref="contentListRef"
              :items="contentList"
              :loading="loading"
              @drag-end="handleDragEnd" />
          </div>

          <!-- Search Results -->
          <div v-if="isSearch" class="search-wrapper">
            <SearchResults
              :results="goodsList"
              :search-query="searchValue"
              :loading="searchLoading"
              :total-count="goodsList.length"
              @clear="handleSearchClear"
              @item-drag-start="handleDragStart"
              @item-drag-end="handleDragEnd"
              @item-drop="handleDrop" />
          </div>
        </div>
      </template>

      <template #kitchen>
        <div class="coming-soon">
          <img class="coming-soon-image" src="@/assets/images/expect.png" alt="暂无搜索结果， 请重新输入" />
          <span class="coming-soon-text">暂未开放，敬请期待</span>
        </div>
      </template>
    </CustomTabs>
  </div>
</template>

<script setup>
  import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
  import CustomTabs from '@/components/customTabs/customTabs.vue';
  import { _API_getIntelligentData } from '@/api/intelligentApi';
  import { _API_getComponentList } from '@/api/sceneApi';

  // Import new extracted components
  import SearchBar from './SearchBar.vue';
  import CategoryGrid from './CategoryGrid.vue';
  import CategoryTabs from './CategoryTabs.vue';
  import ContentList from './ContentList.vue';
  import SearchResults from './SearchResults.vue';
  import { handleOssImageUrl } from '@/utils/tools';
  import { CANVAS_IMG_COMPRESS_PARAMS } from '@/config/const';
  // Component definition
  defineOptions({
    name: 'SidebarSecondaryPanel',
  });

  // Reactive state
  const isSearch = ref(false);
  const searchLoading = ref(false);
  const loading = ref(false); // 首次加载
  const goodsList = ref([]);

  // Dynamic height calculation refs
  const sidebarPanelRef = ref(null);
  const fixedHeaderRef = ref(null);
  const scrollableContentRef = ref(null);
  const scrollableHeight = ref(0);
  let resizeObserver = null;

  const secondaryTabs = ref([
    {
      key: 'furniture',
      title: '公共库',
    },
    {
      key: 'kitchen',
      title: '个人库',
    },
  ]);

  // 主分类数据
  const mainCategories = ref([]);
  const activeMainCategoryIdx = ref('');
  const secondaryTabType = ref('furniture');

  const allCategories = ref([]);
  const contentList = ref([]);

  const searchValue = ref('');
  const activeCategoryIdx = ref("all");
  const contentListRef = ref(null);

  // Emits
  const emit = defineEmits(['drag-start', 'drag-end', 'drop']);

  /**
   * @description 滚动内容区域到顶部
   */
  const scrollToTop = () => {
    nextTick(() => {
      const contentWrapper = scrollableContentRef.value?.querySelector('.content-wrapper');
      const searchWrapper = scrollableContentRef.value?.querySelector('.search-wrapper');

      // 根据当前模式滚动对应的容器
      if (isSearch.value && searchWrapper) {
        searchWrapper.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } else if (!isSearch.value && contentWrapper) {
        contentWrapper.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }
    });
  };

  // Event handlers for new components
  const handleMainCategorySelect = (category) => {
    activeMainCategoryIdx.value = category.key;
    allCategories.value = category.subModules || [];
    activeCategoryIdx.value = "all";
    if (allCategories.value.length > 0) {
      contentList.value = allCategories.value[0].items;
      scrollToTop();
    }
    // 滚动到顶部
  };

  const handleCategoryTabChange = (item) => {
    activeCategoryIdx.value = item.key;
    contentList.value = item.items || [];
    scrollToTop();
  };

  const handleSearchBack = () => {
    isSearch.value = false;
    searchValue.value = '';
    goodsList.value = [];

    // 滚动到顶部
    scrollToTop();
  };

  const handleSearchClear = () => {
    searchValue.value = '';
    goodsList.value = [];
    if (isSearch.value) {
      isSearch.value = false;
    }

    // 滚动到顶部
    scrollToTop();
  };

  /**
   * @description 拖拽开始
   * @param event
   */
  const handleDragStart = (event) => {
    emit('drag-start', {
      action: 'deviceDrag',
      payload: event,
    });
  };

  /**
   * @description 拖拽结束
   * @param event
   */
  const handleDragEnd = (event) => {
    emit('drag-end', {
      action: 'deviceDrag',
      payload: event,
    });
  };

  /**
   * @description 处理放置事件
   * @param event
   */
  const handleDrop = (event) => {
    emit('drop', {
      action: 'deviceDrop',
      payload: event,
    });
  };

  /**
   * @description 计算可滚动内容区域的高度
   */
  const calculateScrollableHeight = () => {
    if (!sidebarPanelRef.value || !fixedHeaderRef.value || !scrollableContentRef.value) {
      return;
    }

    nextTick(() => {
      const panelHeight = sidebarPanelRef.value.clientHeight;
      const headerHeight = fixedHeaderRef.value.offsetHeight;
      const panelPadding = 80; // 20px top + 20px bottom padding from .sidebar-secondary-panel

      const newHeight = panelHeight - headerHeight - panelPadding;
      scrollableHeight.value = Math.max(0, newHeight);

      // 调试信息
      console.log('Height calculation:', {
        panelHeight,
        headerHeight,
        panelPadding,
        newHeight,
        scrollableHeight: scrollableHeight.value,
      });
    });
  };

  /**
   * @description 初始化ResizeObserver监听高度变化
   */
  const initResizeObserver = () => {
    if (!window.ResizeObserver) {
      // Fallback for browsers that don't support ResizeObserver
      calculateScrollableHeight();
      return;
    }

    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === sidebarPanelRef.value || entry.target === fixedHeaderRef.value) {
          calculateScrollableHeight();
          break;
        }
      }
    });

    // 监听sidebar面板的尺寸变化
    if (sidebarPanelRef.value) {
      resizeObserver.observe(sidebarPanelRef.value);
    }

    // 监听固定头部的尺寸变化
    if (fixedHeaderRef.value) {
      resizeObserver.observe(fixedHeaderRef.value);
    }
  };

  /**
   * @description 清理ResizeObserver
   */
  const cleanupResizeObserver = () => {
    if (resizeObserver) {
      resizeObserver.disconnect();
      resizeObserver = null;
    }
  };

  /**
   * @description 处理搜索
   * @param {string} value - 搜索值
   */
  const handleSearch = (value) => {
    // 如果搜索值为空，直接返回
    // if (!value || value.trim() === '') {
    //   return;
    // }

    // 防止重复搜索相同的内容
    if (searchLoading.value) {
      return;
    }

    searchLoading.value = true;

    // 如果不在搜索模式，切换到搜索模式
    if (!isSearch.value) {
      isSearch.value = true;
    }

    // 清空之前的搜索结果
    goodsList.value = [];

    // 滚动到顶部
    scrollToTop();

    _API_getComponentList({ query: value })
      .then((res) => {
        if (res.code === '0') {
          goodsList.value = (res.data || []).map((item) => ({
            ...item,
            id: item.baseSubModuleId,
            name: item.componentDesc,
            img: item.mainImage,
            compressImgUrl: handleOssImageUrl(item.mainImage, CANVAS_IMG_COMPRESS_PARAMS),
            size: item.depthSize,
            price: item.pcPrice,
          }));
        }
      })
      .catch((error) => {
        console.error('搜索失败:', error);
        // 搜索失败时清空结果
        goodsList.value = [];
      })
      .finally(() => {
        searchLoading.value = false;
      });
  };

  /**
   * @description 获取分类数据
   */
  const getCategoryData = async () => {
    try {
      loading.value = true
      const res = await _API_getIntelligentData();
      const { code, data } = res;

      if (code === '0' && data && data.length > 0) {
        // 处理数据转换
        const transformedData = data.map((item) => {
          // Collect all components from all subModules for the "All" category
          const allComponents = item.subModules?.flatMap(subItem =>
            subItem.components?.map((component) => ({
              ...component,
              id: component.baseSubModuleId,
              name: component.componentDesc,
              img: component.mainImage,
              compressImgUrl: handleOssImageUrl(component.mainImage, CANVAS_IMG_COMPRESS_PARAMS),
              size: component.depthSize,
              price: component.pcPrice,
            })) || []
          ) || [];
          console.log("allComponents", allComponents);

          return {
            key: item.systemId,
            label: item.systemName,
            subModules: [
              // Add "All Categories" option first
              {
                key: 'all',
                label: '全部',
                items: allComponents
              },
              // Then add existing subModules
              ...(item.subModules?.map((subItem) => ({
                key: subItem.baseSubModuleId,
                label: subItem.subModuleName,
                items: subItem.components?.map((component) => ({
                  ...component,
                  id: component.baseSubModuleId,
                  name: component.componentDesc,
                  img: component.mainImage,
                  compressImgUrl: handleOssImageUrl(component.mainImage, CANVAS_IMG_COMPRESS_PARAMS),
                  size: component.depthSize,
                  price: component.pcPrice,
                })) || [],
              })) || [])
            ]
          };
        });

        mainCategories.value = transformedData;

        if (transformedData.length > 0) {
          const firstCategory = transformedData[0];
          // 初始化一级
          activeMainCategoryIdx.value = firstCategory.key;
          allCategories.value = firstCategory.subModules || [];
          // 初始化二级
          if (allCategories.value.length > 0) {
            const secondeCategory = allCategories.value[0] || [];
            activeCategoryIdx.value = secondeCategory.key;
            contentList.value = secondeCategory.items || [];
          }
        }
      }
      loading.value = false;
    } catch (error) {
      console.error('Failed to fetch category data:', error);
      loading.value = false;
    }
  };

  // 监听搜索状态变化，重新计算高度
  watch([isSearch], () => {
    nextTick(() => {
      // 给组件切换一些时间来完成渲染
      setTimeout(() => {
        calculateScrollableHeight();
      }, 50);
    });
  });

  // 生命周期钩子
  onMounted(() => {
    // 使用多重延迟确保DOM完全渲染
    nextTick(() => {
      setTimeout(() => {
        initResizeObserver();
        calculateScrollableHeight();
      }, 100); // 给DOM渲染一些时间
    });
  });

  onUnmounted(() => {
    cleanupResizeObserver();
  });

  // 暴露方法供调试使用
  const debugHeightCalculation = () => {
    console.log('=== Debug Height Calculation ===');
    console.log('sidebarPanelRef:', sidebarPanelRef.value);
    console.log('fixedHeaderRef:', fixedHeaderRef.value);
    console.log('scrollableContentRef:', scrollableContentRef.value);

    if (sidebarPanelRef.value) {
      console.log('Panel dimensions:', {
        clientHeight: sidebarPanelRef.value.clientHeight,
        offsetHeight: sidebarPanelRef.value.offsetHeight,
        scrollHeight: sidebarPanelRef.value.scrollHeight,
      });
    }

    if (fixedHeaderRef.value) {
      console.log('Header dimensions:', {
        clientHeight: fixedHeaderRef.value.clientHeight,
        offsetHeight: fixedHeaderRef.value.offsetHeight,
        scrollHeight: fixedHeaderRef.value.scrollHeight,
      });
    }

    calculateScrollableHeight();
  };

  // 在开发环境下暴露调试方法到全局
  if (process.env.NODE_ENV === 'development') {
    window.debugHeightCalculation = debugHeightCalculation;
  }

  // 初始化数据
  getCategoryData();
</script>

<style lang="stylus" scoped>
  /* ==========================================================================
   MAIN CONTAINER - Fixed Height Layout
   ========================================================================== */

  .sidebar-secondary-panel {
    /* 占满父容器的完整高度 */
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px 0;
    position: relative;
    overflow: hidden; /* 防止内容溢出 */

    /* Remove GPU acceleration to avoid DragWrapper conflicts */
  }

  /* ==========================================================================
   FIXED HEADER SECTION - Stays at top, never scrolls
   ========================================================================== */

  .fixed-header {
    /* Fixed positioning at top */
    position: relative;
    z-index: 10;
    flex-shrink: 0; /* Never shrink */
    padding: 0 12px;
    /* Layout */
    display: flex;
    flex-direction: column;
    gap: 12px;

    /* Background and styling */
    background: #fff;

    /* Performance optimization */
    will-change: auto; /* Header doesn't need transform optimization */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  .header-search {
    /* Search bar container */
    flex-shrink: 0;
  }

  .header-categories {
    /* Category grid container */
    flex-shrink: 0;
  }

  .header-tabs {
    /* Category tabs container */
    flex-shrink: 0;
    padding-bottom: 8px;
  }

  /* ==========================================================================
   SCROLLABLE CONTENT SECTION - High Performance Scrolling
   ========================================================================== */

  .scrollable-content {
    /* 动态高度由JavaScript计算并通过内联样式设置 */
    width: 100%;
    overflow: hidden;
    position: relative;
  }

  .content-wrapper,
  .search-wrapper {
    /* 占满动态计算的高度 */
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    /* 平滑滚动 */
    scroll-behavior: smooth;
  }

  /* ==========================================================================
   COMING SOON PLACEHOLDER
   ========================================================================== */

  .coming-soon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 280px;
    &-image {
      width: 80px;
      height: 80px;
      object-fit: contain;
    }
    &-text {
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    outline: 2px solid var(--opn-color-primary, #be965a);
    outline-offset: 2px;
    transition: outline-offset 0.1s ease;
  }
</style>
