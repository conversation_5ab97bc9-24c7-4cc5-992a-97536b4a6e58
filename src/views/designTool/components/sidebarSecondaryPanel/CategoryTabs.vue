<template>
  <TransitionGroup name="tab-item" tag="div" class="category-tabs-list">
    <div
      v-for="(category) in categories"
      :key="category.key"
      :class="[
              'category-tab-item',
              {
                'active': category.key === activeIndex,
                'disabled': category.disabled
              }
            ]"
      @click="handleTabClick(category)"
    >
      <!-- Circular Icon Container -->
      <div
        class="category-icon-circle"
        :class="{ 'active': category.key === activeIndex }"
      >
        <div
          class="category-icon-inner"
        >
          <client-img :src="getIconById(category)" :lazy="false" :ignoreCompress="true" :preview="false" :alt="category.label"></client-img>
        </div>
      </div>

      <!-- Category Label -->
      <span class="category-label">{{ category.label }}</span>
    </div>
  </TransitionGroup>
</template>

<script setup>
  import ClientImg from '@/components/clientImg/clientImg.vue';

  import allIcon from '@/assets/images/devices/all.png';
  import defaultIcon from '@/assets/images/devices/default.png';
  import brainScreenIcon from '@/assets/images/devices/brain_screen.png';
  import switchIcon from '@/assets/images/devices/switch.png';
  import socketIcon from '@/assets/images/devices/socket.png';
  import spotlightIcon from '@/assets/images/devices/spotlight.png';
  import lightStripIcon from '@/assets/images/devices/light_strip.png';
  import ambientLightingIcon from '@/assets/images/devices/ambient_lighting.png';
  import magneticLampIcon from '@/assets/images/devices/magnetic_lamp.png';
  import monitorIcon from '@/assets/images/devices/monitor.png';
  import sensorIcon from '@/assets/images/devices/sensor.png';
  import motorIcon from '@/assets/images/devices/motor.png';
  import gatewayIcon from '@/assets/images/devices/gateway.png';
  import setIcon from '@/assets/images/devices/set.png';
  import speakersIcon from '@/assets/images/devices/speakers.png';
  import musicIcon from '@/assets/images/devices/music.png';
  import driveIcon from '@/assets/images/devices/drive.png';

defineOptions({
  name: 'CategoryTabs'
})
// Props
defineProps({
  categories: {
    type: Array,
    default: () => []
  },
  activeIndex: {
    type: String,
    default: "all"
  },
})

// Emits
const emit = defineEmits(['tab-click', 'tab-change', 'scroll'])

  // {
  //   "2075": "大脑屏",
  //   "2109": "开关",
  //   "2110": "插座",
  //   "2127": "遥控器",
  //   "2068": "RGBCW灯带控制器",
  //   "2083": "射灯",
  //   "2103": "灯带" ,
  //   "2107": "氛围灯",
  //   "2121": "磁吸灯",
  //   "2084": "摄像机",
  //   "2114": "传感器",
  //   "2073": "窗帘电机",
  //   "2100": "电机",
  //   "2113": "网关",
  //   "2089": "影院套装",
  //   "2119": "音箱",
  //   "2120": "音乐主机"
  // }

const iconMap = {
  "all": allIcon,
  "2075": brainScreenIcon,
  "2109": switchIcon,
  "2110": socketIcon,
  "2083": spotlightIcon,
  "2103": lightStripIcon,
  "2107": ambientLightingIcon,
  "2121": magneticLampIcon,
  "2084": monitorIcon,
  "2114": sensorIcon,
  "2073": motorIcon,
  "2113": gatewayIcon,
  "2089": setIcon,
  "2119": speakersIcon,
  "2120": musicIcon,
  "2068": driveIcon,
}


const getIconById = (category) => {
  return iconMap[category.key] || defaultIcon;
}

const handleTabClick = (category) => {
  emit('tab-change', category)
}
</script>

<style scoped lang="stylus">
.category-tabs-container {
  position: relative;
  background: #ffffff;
  width: 100%;
}

.category-tabs-wrapper {
  width: 100%;
}

.category-tabs-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  grid-auto-flow: row;
}

.category-tab-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease-in-out;
  will-change: transform;
  justify-self: center;
}

.category-tab-item:hover:not(.disabled) {
  transform: translateY(-1px);
}

.category-tab-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Circular Icon Background - Matching Figma exactly */
.category-icon-circle {
  position: relative;
  width: 42px;
  height: 42px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: 1px transparent solid;
  transition: all 0.3s ease-in-out;
  will-change: transform, box-shadow, outline-color;
  &.active {
    transform: scale(1.05);
    box-shadow: 0 0 0 2px #FFFBF0;
    outline-color: #BF8630;
  }
}

/* Inner gradient circle */
.category-icon-inner {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
}

/* Icon styling */
.category-icon {
  position: relative;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.85);
  z-index: 1;
}

/* Icon placeholder - 12px x 12px */
.icon-placeholder {
  width: 18px;
  height: 18px;
  background: #FFFFFF;
  border-radius: 2px;
  display: block;
}

/* Category Label - Matching Figma typography */
.category-label {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 11px;
  font-weight: 400;
  line-height: 1.8181818181818181;
  text-align: center;
  color: rgba(0, 0, 0, 0.85);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 48px;
  transition: all 0.3s ease-in-out;
}

/* Active state label */
.category-tab-item.active .category-label {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

/* Disabled state label */
.category-tab-item.disabled .category-label {
  opacity: 0.65;
}
</style>
