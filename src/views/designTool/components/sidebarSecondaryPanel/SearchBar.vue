<template>
  <div class="search-bar">
    <Transition name="search-back" mode="out-in">
      <div v-if="isSearchMode" class="search-back-btn">
        <Icon
          icon="synArrowLeft"
          @click="handleBackClick"
        />
      </div>
    </Transition>
    <div class="search-input-wrapper">
      <a-input-search
        v-model:value.trim="searchValue"
        :placeholder="placeholder"
        :loading="loading"
        enter-button
        size="large"
        :maxlength="10"
        allowClear
        @pressEnter="handleSearch"
        @input="handleInput"
        @search="handleSearch"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps } from 'vue'
import Icon from '@/components/icon/icon.vue'

// Component definition
defineOptions({
  name: 'SearchBar'
})

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '搜索'
  },
  loading: {
    type: <PERSON>olean,
    default: false
  },
  isSearchMode: {
    type: <PERSON>olean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'update:modelValue',
  'search',
  'clear',
  'back',
  'input'
])

// Reactive state
const searchValue = ref(props.modelValue)

// Watch for external value changes
watch(() => props.modelValue, (newValue) => {
  searchValue.value = newValue
})

// Watch for internal value changes
watch(searchValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// Methods
const handleSearch = (event) => {
  const value = event?.target?.value || searchValue.value
  if (!value) {
    return;
  }
  emit('search', value)
}

const handleInput = (event) => {
  if (event.target.value) {
    emit('input', event.target.value)
  }
}

// const handleClear = () => {
//   searchValue.value = ''
//   emit('clear')
// }

const handleBackClick = () => {
  emit('back')
}
</script>

<style scoped>
.search-bar {
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.search-back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 24px;
  height: 40px;
  cursor: pointer;
  transition: all 0.2s ease;
  :deep(i) {
    color: #595959;
    transition: color 0.2s ease;
  }
  &:hover {
    :deep(i) {
      color: #CCA054;
    }
  }
  &:active {
    :deep(i) {
      color: #99641F;
    }
  }
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

/* Responsive design */
@media (max-width: 768px) {
  .search-bar {
    padding: 6px;
    gap: 6px;
  }
  
  .search-back-btn {
    width: 20px;
    height: 40px;
  }
}
</style>
