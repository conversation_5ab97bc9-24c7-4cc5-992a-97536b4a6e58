<template>
  <Transition name="grid-fade" mode="out-in">

    <div v-if="!hidden" class="category-grid">
      <div
        class="category-grid-wrapper"
      >
        <div
          v-for="(category, index) in categories"
          :key="category.key"
          :class="[
              'category-grid-item',
              {
                'active': category.key === activeKey,
                'loading': loading
              }
            ]"
          @click="handleCategoryClick(category)"
        >
          <div class="category-content">
            <div v-if="category.icon" class="category-icon">
              <Icon :icon="category.icon" />
            </div>
            <span class="category-label">{{ category.label }}</span>
            <div v-if="category.count" class="category-count">
              {{ category.count }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import Icon from '@/components/icon/icon.vue'

// Component definition
defineOptions({
  name: 'CategoryGrid'
})

// Props
defineProps({
  categories: {
    type: Array,
    default: () => [],
    validator: (categories) => {
      return categories.every(cat => 
        cat && typeof cat === 'object' && 
        cat.key && cat.label
      )
    }
  },
  activeKey: {
    type: String,
    default: "all"
  },
  columns: {
    type: Number,
    default: 3,
    validator: (value) => value > 0 && value <= 6
  },
  loading: {
    type: Boolean,
    default: false
  },
  hidden: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['category-select'])

// Methods
const handleCategoryClick = (category) => {
  emit('category-select', category)
}
</script>

<style scoped>
.category-grid {
  width: 100%;
}

.category-grid-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  width: 100%;
}

/* Grid item matching Figma design exactly */
.category-grid-item {
  position: relative;
  padding: 2px;
  background-color: #F5F5F5;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  min-height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  height: 24px;
  will-change: background-color;
  &.active {
    background-color: #FFF7E6;
  }
  &:hover {
    background-color: #F0F0F0;
    &.active {
      background-color: #FFF7E6;
    }
  }
  &:active {
    background-color: #FFF7E6;
    .category-label {
      color: #BF8630;
    }
  }
}

/* Content layout - simplified */
.category-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0;
  position: relative;
  z-index: 1;
  width: 100%;
}

/* Icon styling - hidden for now to match Figma */
.category-icon {
  display: none;
}

/* Typography matching Figma exactly */
.category-label {
  font-size: 12px;
  font-weight: 400;
  text-align: center;
  line-height: 1.67;
  transition: all 0.3s ease-in-out;
  color: rgba(0, 0, 0, 0.85);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.category-grid-item.active .category-label {
  font-weight: 500;
  color: #BF8630;
}

/* Count badge - hidden for now to match Figma */
.category-count {
  display: none;
}
</style>
