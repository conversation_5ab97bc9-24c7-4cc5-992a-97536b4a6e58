<template>
  <div class="content-list-container">
    <!-- Simplified content grid - display items directly -->
    <div class="content-grid">
      <div
        v-for="(item, index) in items"
        :key="`${item.id}_${index}`"
        class="grid-item-wrapper"
      >
        <ProductHoverWrapper :product-data="item">
          <div data-draggable class="grid-item">
            <DragWrapper
              :drag-data="item"
              :drag-type="'toggle-item'"
              @drag-start="handleDragStart"
              @drag-end="handleDragEnd"
              @drop="handleDrop">
              <GoodsCard :border="false" :stop-propagation="false" :goods-obj="item" />
            </DragWrapper>
          </div>
        </ProductHoverWrapper>
      </div>
    </div>

    <!-- Empty state -->
    <div v-if="isEmpty" class="empty-state">
      <slot name="empty">
        <div class="empty-content">
          <Icon icon="synEmpty" class="empty-icon" />
          <p class="empty-text">暂无内容</p>
        </div>
      </slot>
    </div>

    <!-- Loading state with skeleton -->
    <div v-if="loading || searching" class="loading-state">
      <slot name="loading">
        <!-- 骨架屏 - 保持一行两列布局 -->
        <div class="skeleton-grid">
          <div
            v-for="n in 10"
            :key="`skeleton-${n}`"
            class="skeleton-item-wrapper"
          >
            <div class="skeleton-item">
              <a-skeleton
                active
                :paragraph="{ rows: 3, width: ['100%', '80%', '60%'] }"
                :title="false"
                :avatar="{ size: 'large', shape: 'square' }"
              >
                <template #avatar>
                  <div class="skeleton-image"></div>
                </template>
              </a-skeleton>
            </div>
          </div>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue';
  import DragWrapper from '@/views/designTool/components/dragWrapper/dragWrapper.vue';
  import GoodsCard from '@/components/goodsCard/goodsCard.vue';
  import Icon from '@/components/icon/icon.vue';
  import ProductHoverWrapper from './ProductHoverWrapper.vue';

  // Component definition
  defineOptions({
    name: 'ContentList',
  });

  // Props
  const props = defineProps({
    items: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    searching: {
      type: Boolean,
      default: false,
    },
  });

  // Emits
  const emit = defineEmits(['item-drag-start', 'item-drag-end', 'drag-end', 'item-drop']);

  // Computed
  const isEmpty = computed(() => {
    return !props.loading && !props.searching && (!props.items || props.items.length === 0);
  });

  /**
   * @description 处理拖拽开始事件
   */
  const handleDragStart = (event) => {
    emit('item-drag-start', event);
  };

  /**
   * @description 处理拖拽结束事件
   */
  const handleDragEnd = (event) => {
    emit('drag-end', event);
  };

  /**
   * @description 处理放置事件
   */
  const handleDrop = (event) => {
    emit('item-drop', event);
  };
</script>

<style lang="stylus" scoped>
  .content-list-container {
    flex: 1;
    width: 100%;
    padding 0 12px;
  }

  .content-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    width: 100%;
    box-sizing: border-box;
    padding-bottom: 20px; /* Add bottom padding since we removed section padding */
  }

  .grid-item-wrapper {
    /* 计算每个项目的宽度：(100% - gap) / 2 */
    width: calc(50% - 4px);
    flex-shrink: 0;
    flex-grow: 0;
    box-sizing: border-box;
    min-width: 0; /* 防止内容溢出 */
  }

  /* 解决奇数项目时最后一个空白的问题 - 针对每个分类内部 */
  .content-grid .grid-item-wrapper:last-child:nth-child(odd) {
    width: 100%; /* 如果是分类内最后一个且为奇数位置，占满整行 */
  }

  /* 更强的规则：确保任何奇数位置的最后一个元素都能正确处理 */
  .content-grid .grid-item-wrapper:nth-last-child(1):nth-child(odd) {
    width: 100% !important;
  }

  /* JavaScript控制的最后奇数项目样式 */
  .grid-item-wrapper.last-odd-item {
    width: 100% !important;
  }

  .grid-item {
    width: 100%;
    height: auto; /* 改为auto，让内容决定高度 */
    min-height: 140px; /* 设置最小高度确保卡片完整显示 */
    display: flex;
    flex-direction: column;
    background-color: #f8f7f8;
    border-radius: 10px;
    transition: all 0.2s ease;
    overflow: hidden;
    box-sizing: border-box;
    /* 确保卡片内容不会被截取 */
    word-wrap: break-word;
    word-break: break-word;
    will-change: box-shadow;
    cursor pointer;
  }

  .grid-item:hover {
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.20);
  }
  .grid-item:active {
    box-shadow: none;
    background: #F8F7F8;
  }

  /* Fine-tune GoodsCard styling to match Figma design */
  .grid-item :deep(.goods-wrap) {
    background: #f8f7f8;
    border-radius: 10px;
    padding: 8px;
    height: 100%;
    min-height: 140px; /* 确保卡片有足够的高度 */
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between; /* 内容分布均匀 */
  }

  .grid-item :deep(.top-img) {
    height: 88px;
    width: 100%;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 4px;
    flex-shrink: 0; /* 防止图片被压缩 */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .grid-item :deep(.top-img img) {
    width: 100%;
    height: 100%;
    object-fit: contain; /* 确保图片完整显示 */
    object-position: center;
  }


  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    padding: 40px 20px;
  }

  .loading-state {
    padding: 0;
    min-height: auto;
  }

  /* 骨架屏样式 - 保持与商品卡片相同的布局 */
  .skeleton-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    width: 100%;
    box-sizing: border-box;
  }

  .skeleton-item-wrapper {
    width: calc(50% - 4px);
    flex-shrink: 0;
    flex-grow: 0;
    box-sizing: border-box;
    min-width: 0;
  }

  /* 骨架屏最后一个奇数项目占满整行 */
  .skeleton-item-wrapper:last-child:nth-child(odd) {
    width: 100%;
  }

  .skeleton-item {
    width: 100%;
    min-height: 140px;
    background-color: #f8f7f8;
    border-radius: 10px;
    padding: 8px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }

  .skeleton-image {
    width: 100%;
    height: 88px;
    background-color: #f0f0f0;
    border-radius: 6px;
    margin-bottom: 4px;
    animation: skeleton-loading 1.5s ease-in-out infinite alternate;
  }

  /* 骨架屏动画 */
  @keyframes skeleton-loading {
    0% {
      background-color: #f0f0f0;
    }
    100% {
      background-color: #e0e0e0;
    }
  }

  /* 自定义骨架屏内容样式 */
  .skeleton-item :deep(.ant-skeleton) {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .skeleton-item :deep(.ant-skeleton-content) {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 4px 0;
  }

  .skeleton-item :deep(.ant-skeleton-paragraph) {
    margin-bottom: 0;
  }

  .skeleton-item :deep(.ant-skeleton-paragraph li) {
    height: 12px;
    margin-bottom: 4px;
    border-radius: 4px;
  }

  .skeleton-item :deep(.ant-skeleton-paragraph li:last-child) {
    margin-bottom: 0;
  }

  .empty-content,
  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    text-align: center;
  }

  .empty-icon {
    width: 48px;
    height: 48px;
    opacity: 0.4;
    color: #ccc;
  }

  .empty-text,
  .loading-text {
    font-size: 14px;
    color: #999;
    margin: 0;
    line-height: 1.4;
  }

  /* Removed content-section transitions since we simplified the structure */

  /* Cross-browser compatibility for Flex layout */
  .content-grid {
    display: flex;
    flex-wrap: wrap;
  }

  .grid-item-wrapper {
    flex-basis: calc(50% - 4px);
    flex-grow: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
  }

  /* Ensure consistent box-sizing */
  .content-grid,
  .grid-item-wrapper,
  .grid-item,
  .grid-item *,
  .grid-item *::before,
  .grid-item *::after {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
  }
</style>
