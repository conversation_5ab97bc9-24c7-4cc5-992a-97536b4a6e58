<template>
  <div v-if="!hidden" class="search-results">
    <slot name="default" :results="results" :loading="loading" :empty="isEmpty">
      <!-- Default search results using SearchGoods component -->
      <SearchGoods
        :goods-list="results"
        :loading="loading"
        :searching="loading"
        @item-click="handleItemClick"
        @item-drag-start="handleItemDragStart"
        @item-drag-end="handleItemDragEnd"
        @item-drop="handleItemDrop"
      />
    </slot>
  </div>
</template>

<script setup>
import { computed, defineProps, defineEmits } from 'vue'
import SearchGoods from '@/views/designTool/components/sidebarSecondaryPanel/searchGoods/searchGoods.vue'

// Component definition
defineOptions({
  name: 'SearchResults'
})

// Props
const props = defineProps({
  results: {
    type: Array,
    default: () => []
  },
  searchQuery: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  hidden: {
    type: <PERSON><PERSON>an,
    default: false
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  showClearButton: {
    type: Boolean,
    default: true
  },
  totalCount: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits([
  'clear',
  'item-click',
  'item-drag-start',
  'item-drag-end',
  'item-drop'
])

// Computed
const isEmpty = computed(() => {
  return !props.loading && (!props.results || props.results.length === 0)
})

// Methods
const handleItemClick = (item) => {
  emit('item-click', item)
}

const handleItemDragStart = (event) => {
  emit('item-drag-start', event)
}

const handleItemDragEnd = (event) => {
  emit('item-drag-end', event)
}

const handleItemDrop = (event) => {
  emit('item-drop', event)
}
</script>

<style scoped>
.search-results {
  /* Remove internal height constraints */
  width: 100%;
  padding: 12px 0;
}
</style>
