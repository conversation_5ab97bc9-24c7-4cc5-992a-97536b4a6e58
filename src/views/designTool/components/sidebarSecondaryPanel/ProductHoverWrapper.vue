<template>
  <a-popover :mouse-leave-delay="0" placement="right" overlay-class-name="product-hover-popover">
    <template #content>
      <ProductHoverCard :product-data="productData" />
    </template>
    <slot></slot>
  </a-popover>
</template>

<script setup>
import ProductHoverCard from './ProductHoverCard.vue'

// Component definition
defineOptions({
  name: 'ProductHoverWrapper'
})

// Props
defineProps({
  productData: {
    type: Object,
    required: true,
    default: () => ({})
  }
})
</script>

<style>
/* 全局样式，确保只影响product-hover-popover类的popover */
.product-hover-popover.ant-popover .ant-popover-inner {
  padding: 0 !important;
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.product-hover-popover.ant-popover .ant-popover-inner-content {
  padding: 0 !important;
}

.product-hover-popover.ant-popover .ant-popover-content {
  padding: 0 !important;
}

/* 隐藏箭头 */
.product-hover-popover.ant-popover .ant-popover-arrow {
  display: none !important;
}
</style>

