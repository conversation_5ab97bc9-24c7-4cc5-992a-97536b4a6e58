<script setup>
  import { _API_getListByMk, _API_getSceneListByRoomId } from '@/api/sceneApi';
  import Icon from '@/components/icon/icon.vue'
  import PageLayout from '@/layouts/pageLayout.vue';
  import { useCaseDataStore } from '@/store/modules/caseDataStore';
  import { SET_INTELLIGENT_SCENE_STATE, useGlobalModalStore } from '@/store/modules/globalModalStore';
  import AddProductModel from '@/components/productModel/productModel.vue';
  import RoomProductList from '@/views/designTool/components/intelligentScene/roomProductList.vue';
  import SelectedScene from '@/views/designTool/components/intelligentScene/selectedScene.vue';
  import { message } from '@syn/ant-design4-vue3';
  import { computed, onMounted, onUnmounted, ref, nextTick, watch } from 'vue';
  import { debounce } from 'lodash-es';
  // 导入Swiper
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { Navigation, Mousewheel } from 'swiper/modules';
  import 'swiper/css';
  import 'swiper/css/navigation';

  const globalModalStore = useGlobalModalStore();
  const selectedRoom = ref(''); // 选择的空间
  const selectedRoomId = ref(''); // 选择的空间
  const selectedSubModule = ref({}); // 选择的子模块
  const addProductTitle = ref('');
  const selectSceneList = ref([]); // 记录已选择的场景code
  const leftProductList = ref([]); // 对应空间下商品列表
  const addProductVisible = ref(false);
  const rooms = ref([]);
  const productsInSpace = ref([]);
  const sceneProgress = ref([]); // 左侧商品列表
  const devices = ref([]); // 选择的场景列表
  const confirmLoading = ref(false);
  const closeAndSaveOpen = ref(false);
  const createQuotationOpen = ref(false);
  const selectComponentCodes = ref([]);
  const buttonDisabled = computed(() => {
    return allSpaceProductList.value.every((room) => room.goods.length === 0);
  });
  const emit = defineEmits(['onAction', 'createQuotation']);
  const viewBox = ref('0 0 200 200');
  const allSpaceProductList = ref([]);
  const emptySceneNum = ref(0);
  const originalProductList = ref([]); // 存储从设计工具获取的所有商品，保存时与更改后的商品列表做对比，判断商品是新增还是删除

  // Swiper相关的响应式变量
  const swiperRef = ref(null);
  const showNavigation = ref(false);
  const roomListContainer = ref(null);
  const swiperInstance = ref(null);
  
  // 场景进度 Swiper 相关的响应式变量
  const sceneProgressSwiperRef = ref(null);
  const showSceneProgressNavigation = ref(false);
  const sceneProgressContainer = ref(null);
  const sceneProgressSwiperInstance = ref(null);
  
  // 组件高度自适应相关变量
  const bottomSectionRef = ref(null);
  const componentHeight = ref(0);
  const headerHeight = ref(0);
  const topSectionHeight = ref(0);
  onMounted(async () => {
    // 与设计工具交互存储空间列表
    emit('onAction', {
      action: 'getSpaces',
    });
    rooms.value = getStoreVariable()?.sceneSpaceList.map((item, index) => {
      return { ...item, selected: index === 0, selectSceneList: [] };
    });
    getRoomsSvgs(); // 获取各个空间svg并进行比例调整
    if (rooms.value.length > 0) {
      emit('onAction', {
        action: 'getDevices',
      });
      emit('onAction', {
        action: 'getScenes',
      });
      getAllProducts(); // 获取所有空间下商品并进行数据处理
    }

    // 在组件挂载后检测是否需要导航
    await nextTick();
    checkIfNeedNavigation();
    checkIfNeedSceneProgressNavigation();

    // 计算初始组件高度
    calculateComponentHeight();
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);
  });

  // 在组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });

  // 添加检测是否需要显示导航按钮的方法
  const checkIfNeedNavigation = () => {
    nextTick(() => {
      if (roomListContainer.value && swiperRef.value) {
        const swiperElement = swiperRef.value.$el;
        const slidesWrapper = swiperElement.querySelector('.swiper-wrapper');

        if (slidesWrapper) {
          // 由于设置了 overflow: hidden，使用 scrollWidth 和 clientWidth 来检测溢出
          const swiperClientWidth = swiperElement.clientWidth;
          const swiperScrollWidth = slidesWrapper.scrollWidth;

          // 获取 padding 值
          const swiperStyle = window.getComputedStyle(swiperElement);
          const paddingLeft = parseFloat(swiperStyle.paddingLeft);
          const paddingRight = parseFloat(swiperStyle.paddingRight);
          const availableWidth = swiperClientWidth - paddingLeft - paddingRight;

          showNavigation.value = swiperScrollWidth > availableWidth;
        }
      }
    });
  };

  // 添加检测场景进度是否需要显示导航按钮的方法
  const checkIfNeedSceneProgressNavigation = () => {
    nextTick(() => {
      if (sceneProgressContainer.value && sceneProgressSwiperRef.value) {
        const swiperElement = sceneProgressSwiperRef.value.$el;
        const slidesWrapper = swiperElement.querySelector('.swiper-wrapper');

        if (slidesWrapper) {
          // 由于设置了 overflow: hidden，使用 scrollWidth 和 clientWidth 来检测溢出
          const swiperClientWidth = swiperElement.clientWidth;
          const swiperScrollWidth = slidesWrapper.scrollWidth;

          // 获取 padding 值
          const swiperStyle = window.getComputedStyle(swiperElement);
          const paddingLeft = parseFloat(swiperStyle.paddingLeft);
          const paddingRight = parseFloat(swiperStyle.paddingRight);
          const availableWidth = swiperClientWidth - paddingLeft - paddingRight;

          showSceneProgressNavigation.value = swiperScrollWidth > availableWidth;
        }
      }
    });
  };

  // Swiper事件处理
  const onSwiper = (swiper) => {
    swiperInstance.value = swiper;
    // 延迟检测，确保 Swiper 完全初始化
    setTimeout(() => {
      checkIfNeedNavigation();
    }, 100);
  };

  const onSlideChange = () => {
    // 可以在这里添加滑动变化的处理逻辑
  };

  // 场景进度 Swiper 事件处理
  const onSceneProgressSwiper = (swiper) => {
    sceneProgressSwiperInstance.value = swiper;
    // 延迟检测，确保 Swiper 完全初始化
    setTimeout(() => {
      checkIfNeedSceneProgressNavigation();
    }, 100);
  };

  // 监听rooms数据变化，重新检测导航需求
  watch(
    rooms,
    () => {
      nextTick(() => {
        checkIfNeedNavigation();
        calculateComponentHeight();
      });
    },
    { deep: true }
  );

  // 监听sceneProgress数据变化，重新检测场景进度导航需求
  watch(
    sceneProgress,
    () => {
      nextTick(() => {
        checkIfNeedSceneProgressNavigation();
      });
    },
    { deep: true }
  );
  
  // 监听窗口高度变化，重新计算组件高度
  watch(
    () => window.innerHeight,
    () => {
      debouncedCalculateHeight();
    }
  );
  
  // 监听设备列表变化，重新计算组件高度
  watch(
    devices,
    () => {
      nextTick(() => {
        calculateComponentHeight();
      });
    },
    { deep: true }
  );

  // 计算组件高度的函数
  const calculateComponentHeight = () => {
    if (!bottomSectionRef.value) return;
    
    // 获取页面总高度
    const windowHeight = window.innerHeight;
    
    // 获取头部和顶部区域高度
    const headerEl = document.querySelector('.create-proposal-header');
    const topSectionEl = document.querySelector('.top-section');
    
    if (headerEl && topSectionEl) {
      headerHeight.value = headerEl.offsetHeight;
      topSectionHeight.value = topSectionEl.offsetHeight;
      
      // 计算底部区域应有的高度（减去头部和顶部区域的高度，再减去padding）
      const padding = 40; // 上下padding各20px
      componentHeight.value = windowHeight - headerHeight.value - topSectionHeight.value - padding;
    }
  };
  
  // 使用防抖处理的计算高度函数
  const debouncedCalculateHeight = debounce(calculateComponentHeight, 200);
  
  // 监听窗口大小变化
  const handleResize = () => {
    checkIfNeedNavigation();
    checkIfNeedSceneProgressNavigation();
    debouncedCalculateHeight();
  };

  function getRoomsSvgs() {
    let allX = [];
    let allY = [];
    rooms.value.forEach((r) => {
      if (r?.path?.length > 0) {
        const roomX = [];
        const roomY = [];
        r.path.forEach((p, index) => {
          if (index % 2 === 0) {
            roomX.push(p);
            allX.push(p);
          } else {
            roomY.push(p);
            allY.push(p);
          }
        });
        if (roomX.length > 0 && roomY.length > 0) {
          const minX = Math.min(...roomX);
          const minY = Math.min(...roomY);
          const width = Math.max(...roomX) - minX;
          const height = Math.max(...roomY) - minY;
          r.viewBox = `${minX} ${minY} ${width} ${height}`;
        }
      }
    });
    if (allX.length > 0 && allY.length > 0) {
      const minX = Math.min(...allX);
      const minY = Math.min(...allY);
      const width = Math.max(...allX) - minX;
      const height = Math.max(...allY) - minY;
      viewBox.value = `${minX} ${minY} ${width} ${height}`;
    }
  }
  function getAllProducts() {
    let scenes = getStoreVariable().selectSceneList;
    let deviceList = getStoreVariable()?.sceneDeviceList;
    deviceList = deviceList.filter((item) => item.roomId);
    originalProductList.value = JSON.parse(JSON.stringify(deviceList));
    rooms.value.forEach((r) => {
      allSpaceProductList.value.push({
        spaceId: r.roomId,
        uuid: r.roomId,
        goods: [],
        scene: [],
      });
    });
    deviceList.forEach((item) => {
      allSpaceProductList.value.forEach((a) => {
        if (a.spaceId === item.roomId) {
          a.goods.push(item);
        }
      });
    });
    allSpaceProductList.value.forEach((item) => {
      scenes.forEach((s) => {
        if (s.roomId === item.spaceId) {
          item.scene.push(s);
        }
      });
    });
    let productCodes = {};
    allSpaceProductList.value.forEach((item) => {
      if (item.goods.length > 0) {
        productCodes[item.spaceId] = item.goods.map((g) => g.componentCode);
      }
    });
    if (Object.keys(productCodes).length > 0) {
      _API_getSceneListByRoomId({ productCodes: productCodes }).then((res) => {
        let length = 0;
        Object.values(res.data.scenes).forEach((s) => {
          console.log(s);
          length += s.length;
        });
        if (length > 0) {
          // 使用for...of循环，以便能够在找到符合条件的数据后立即退出所有循环
          let foundMatch = false;
          for (const s of Object.keys(res.data.scenes)) {
            if (foundMatch) break; // 如果已找到匹配项，退出外层循环
            for (const a of allSpaceProductList.value) {
              if (a.spaceId === s && res.data.scenes[s].length > 0) {
                selectedRoomId.value = s;
                selectedRoom.value = rooms.value.find((r) => r.roomId === s).name;
                selectRoom(rooms.value.find((room) => room.roomId === a.spaceId));
                foundMatch = true; // 标记已找到匹配项
                break; // 退出内层循环
              }
            }
          }
        } else {
          selectedRoom.value = rooms.value[0].name;
          selectedRoomId.value = rooms.value[0].roomId;
          getRoomProductList();
        }
      });
    } else {
      selectedRoom.value = rooms.value[0].name;
      selectedRoomId.value = rooms.value[0].roomId;
      getRoomProductList();
    }
  }
  /**
   * 获取store中空间列表
   * @returns 返回场景空间列表
   */
  function getStoreVariable() {
    return useCaseDataStore();
  }

  async function getRoomProductList() {
    productsInSpace.value = allSpaceProductList.value;
    let productCodes = [];
    selectSceneList.value = [];
    productsInSpace.value.forEach((p) => {
      rooms.value.forEach((r) => {
        if (p.uuid === r.roomId && r.selected) {
          selectSceneList.value = p.scene.map((s) => s.sceneCode);
          productCodes = p.goods.map((g) => g.componentCode);
        }
      });
    });
    getLeftProductList(allSpaceProductList.value);
    await getSceneList(productCodes);
  }
  function getLeftProductList(val) {
    let productItem = val.find((p) => rooms.value.some((r) => p.uuid === r.roomId && r.selected));
    if (productItem) {
      let list = [];
      productItem.goods.forEach((goods) => {
        let existingItem = list.find((l) => l.baseSubModuleId === goods.baseSubModuleId);
        if (!existingItem) {
          existingItem = {
            baseSubModuleId: goods.baseSubModuleId,
            subModuleName: goods.subModuleName,
            goodIds: [],
          };
          list.push(existingItem);
        }
        const existingGood = existingItem.goodIds.find((g) => g.componentCode === goods.componentCode);
        if (existingGood) {
          existingGood.quantity += goods.quantity || 1;
        } else {
          existingItem.goodIds.push({
            mainImage: goods.mainImage,
            componentName: goods.componentDesc || goods.componentName,
            quantity: goods.quantity || 1,
            componentCode: goods.componentCode,
          });
        }
      });
      leftProductList.value = list;
    }
  }
  /**
   * 获取场景列表
   */
  async function getSceneList(productCodes) {
    const params = {
      productCodes: productCodes,
    };
    await _API_getListByMk(params).then((res) => {
      if (res?.code === '0') {
        sceneProgress.value = res.data.map((item) => {
          return {
            ...item,
            selected: selectSceneList.value.includes(item.marketingCode),
          };
        });
        sceneProgress.value.forEach((item) => {
          if (item.selected) {
            editLeftAndSelectScene(item);
          }
        });
      }
    });
  }
  /**
   * @description 关闭智慧场景设置
   */
  function closePage() {
    closeAndSaveOpen.value = true;
    if (allSpaceProductList.value.length > 0) {
      emptySceneNum.value = allSpaceProductList.value.filter((item) => item.scene.length === 0).length;
    }
  }
  function save(type) {
    confirmLoading.value = true;
    const scenes = allSpaceProductList.value.flatMap((item) => item.scene); // 场景列表处理
    emit('onAction', {
      action: 'setScenes',
      payload: {
        scenes: scenes,
      },
    });
    allSpaceProductList.value.forEach((item) => {
      item.goods = item.goods.map((good) => {
        return {
          ...good,
          roomId: item.spaceId,
        };
      });
    });
    let productList = allSpaceProductList.value.flatMap((item) => item.goods);
    const addedProducts = productList.filter((product) => !product.uuid);
    const deletedProducts = originalProductList.value.filter(
      (original) =>
        !productList.some(
          (product) => product.componentCode === original.componentCode && product.uuid === original.uuid
        )
    );
    addedProducts.forEach((product) => {
      delete product.position;
      emit('onAction', {
        action: 'addDevice',
        payload: product,
      });
    });
    deletedProducts.forEach((product) => {
      emit('onAction', {
        action: 'deleteDevice',
        payload: product.uuid,
      });
    });
    if (type !== 'noMessage') {
      message.success('保存成功');
    }
    if (type !== 'noMessage') {
      globalModalStore.clearStoreState(SET_INTELLIGENT_SCENE_STATE);
    }
    confirmLoading.value = false;
  }
  function handleCloseAndSave() {
    confirmLoading.value = true;
    save();
    confirmLoading.value = false;
    handleClose();
  }
  function handleClose() {
    closeAndSaveOpen.value = false;
    globalModalStore.clearStoreState(SET_INTELLIGENT_SCENE_STATE);
  }
  function handleCancel() {
    closeAndSaveOpen.value = false;
  }
  function addProduct(data) {
    selectedSubModule.value = { ...data };
    addProductTitle.value = data.title;
    selectComponentCodes.value = [];
    leftProductList.value.forEach((item) => {
      item.goodIds.forEach((i) => {
        selectComponentCodes.value.push(i);
      });
    });
    addProductVisible.value = true;
  }
  function changeQuantity(data) {
    const targetSpace = allSpaceProductList.value.find((item) => item.spaceId === selectedRoomId.value);
    if (targetSpace) {
      const goodsInTargetSpace = targetSpace.goods.filter((good) => good.componentCode === data.componentCode);
      const currentCount = goodsInTargetSpace.length;
      const targetCount = data.quantity || 0;
      if (targetCount > currentCount) {
        let good = { ...goodsInTargetSpace[0] };
        good.uuid = '';
        // 增加商品到目标数量
        const addCount = targetCount - currentCount;
        for (let i = 0; i < addCount; i++) {
          targetSpace.goods.push(good);
        }
      } else if (targetCount < currentCount) {
        // 从后往前删除多余的商品直到达到目标数量
        let removeCount = currentCount - targetCount;
        for (let i = targetSpace.goods.length - 1; i >= 0 && removeCount > 0; i--) {
          if (targetSpace.goods[i].componentCode === data.componentCode) {
            targetSpace.goods.splice(i, 1);
            removeCount--;
          }
        }
      }
    }
  }
  async function deleteProduct(data) {
    const targetSpace = allSpaceProductList.value.find((item) => item.spaceId === selectedRoomId.value);
    if (targetSpace) {
      targetSpace.goods = targetSpace.goods.filter((good) => good.componentCode !== data.deleteCode);
    }
    const params = {
      productCodes: data.allComponentCode,
    };
    await _API_getListByMk(params).then((res) => {
      if (res?.code === '0') {
        sceneProgress.value = res.data.map((item) => {
          return {
            ...item,
            selected: selectSceneList.value.includes(item.marketingCode),
          };
        });
      }
    });
    if (data.deleteCode) {
      if (devices.value.length > 0) {
        devices.value.forEach((item) => {
          sceneProgress.value.forEach((s) => {
            if (s.marketingCode === item.marketingCode) {
              item.status = s.status;
            }
          });
          item.subModuleList?.forEach((sub) => {
            sub.productList = sub.productList.filter((p) => p.componentCode !== data.deleteCode);
          });
        });
      }
    }
  }
  async function addProductConfirm(data) {
    // 子模块添加商品
    if (selectedSubModule.value.type === 'subModule') {
      if (data.length > 0) {
        data = data.map((item) => {
          return {
            ...item,
            roomId: selectedRoomId.value,
            uuid: '',
          };
        });
        const targetSpace = allSpaceProductList.value.find((item) => item.spaceId === selectedRoomId.value);
        if (targetSpace) {
          const goodsToAdd = [];
          data.forEach((item) => {
            const quantity = item.quantity || 1;
            for (let i = 0; i < quantity; i++) {
              goodsToAdd.push({ ...item });
            }
          });
          targetSpace.goods = [...targetSpace.goods, ...goodsToAdd];
        }
        let productCodes = []; // productCodes是用于计算场景状态的入参
        // 添加左侧边栏商品
        leftProductList.value.forEach((p) => {
          p.goodIds.forEach((goodId) => {
            productCodes.push(goodId.componentCode);
          });
        });
        // 添加选中的商品
        data.forEach((item) => {
          productCodes.push(item.componentCode);
        });
        // 将data转成跟左侧边栏相同的二维数组，添加进leftProductList
        const tempDataMap = {};
        data.forEach((item) => {
          const key = item.baseSubModuleId;
          if (!tempDataMap[key]) {
            tempDataMap[key] = [];
          }
          tempDataMap[key].push({ ...item });
        });
        const groupedData = Object.values(tempDataMap);
        // 如果leftProductList为空，直接用新数据填充
        if (leftProductList.value.length === 0) {
          leftProductList.value = groupedData.map((group) => {
            const [firstItem] = group;
            return {
              baseSubModuleId: firstItem.baseSubModuleId,
              subModuleName: firstItem.subModuleName,
              goodIds: group.map((i) => ({
                uuid: '',
                componentCode: i.componentCode,
                componentName: i.componentDesc,
                componentDesc: i.componentDesc,
                mainImage: i.mainImage,
                quantity: i.quantity,
              })),
            };
          });
        } else {
          // 合并或新增数据到 leftProductList
          groupedData.forEach((group) => {
            const [firstItem] = group;
            const existingIndex = leftProductList.value.findIndex(
              (s) => s.baseSubModuleId === firstItem.baseSubModuleId
            );
            const formattedItems = group.map((i) => ({
              uuid: '',
              componentCode: i.componentCode,
              componentName: i.componentDesc,
              componentDesc: i.componentDesc,
              mainImage: i.mainImage,
              quantity: i.quantity,
            }));
            if (existingIndex > -1) {
              // 合并数据
              formattedItems.forEach((newItem) => {
                const existingGoodIndex = leftProductList.value[existingIndex].goodIds.findIndex(
                  (good) => good.componentCode === newItem.componentCode
                );
                if (existingGoodIndex > -1) {
                  // 如果商品已存在，增加数量
                  leftProductList.value[existingIndex].goodIds[existingGoodIndex].quantity += newItem.quantity;
                  if (leftProductList.value[existingIndex].goodIds[existingGoodIndex].quantity > 99) {
                    leftProductList.value[existingIndex].goodIds[existingGoodIndex].quantity = 99;
                    message.warning('商品数量超过上限，已为您设置成99个');
                  }
                } else {
                  // 如果商品不存在，添加新商品
                  leftProductList.value[existingIndex].goodIds.push(newItem);
                }
              });
            } else {
              // 新增数据
              leftProductList.value.push({
                baseSubModuleId: firstItem.baseSubModuleId,
                subModuleName: firstItem.subModuleName,
                goodIds: formattedItems,
              });
            }
          });
        }
        // 保持响应式更新
        leftProductList.value = [...leftProductList.value];
        const params = {
          productCodes: productCodes,
        };
        await _API_getListByMk(params).then((res) => {
          if (res?.code === '0') {
            sceneProgress.value = res.data.map((item) => {
              return {
                ...item,
                selected: selectSceneList.value.includes(item.marketingCode),
              };
            });
          }
        });
      }
      if (devices.value.length > 0) {
        devices.value.forEach((item) => {
          sceneProgress.value.forEach((s) => {
            if (s.marketingCode === item.marketingCode) {
              item.status = s.status;
            }
          });
          data.forEach((d) => {
            item.subModuleList.forEach((sub) => {
              if (sub.subModuleId.includes(d.baseSubModuleId)) {
                d.componentName = d.componentDesc;
                // 检查是否已存在相同的componentCode
                const exists = sub.productList.some((product) => product.componentCode === d.componentCode);
                if (!exists) {
                  sub.productList.push(d);
                }
              }
            });
          });
        });
      }
    } else {
      // 左侧商品列表添加商品
      if (data.length > 0) {
        data = data.map((item) => {
          return {
            ...item,
            roomId: selectedRoomId.value,
            uuid: '',
          };
        });
        const targetSpace = allSpaceProductList.value.find((item) => item.spaceId === selectedRoomId.value);
        if (targetSpace) {
          // 创建一个Map来存储data中每个componentCode对应的数量
          const dataComponentCodeMap = new Map();
          data.forEach((item) => {
            const code = item.componentCode;
            const quantity = item.quantity || 1;
            dataComponentCodeMap.set(code, quantity);
          });
          // 创建一个Map来存储targetSpace.goods中每个componentCode已有的数量
          const existingComponentCodeMap = new Map();
          targetSpace.goods.forEach((item) => {
            const code = item.componentCode;
            existingComponentCodeMap.set(code, (existingComponentCodeMap.get(code) || 0) + 1);
          });
          // 处理在data中存在的componentCode
          const newGoods = [];
          dataComponentCodeMap.forEach((quantity, code) => {
            const existingCount = existingComponentCodeMap.get(code) || 0;
            if (quantity > existingCount) {
              // 需要添加
              const itemToAdd = data.find((item) => item.componentCode === code);
              for (let i = 0; i < quantity - existingCount; i++) {
                newGoods.push({ ...itemToAdd, roomId: selectedRoomId.value, uuid: '' });
              }
            } else if (quantity < existingCount) {
              // 需要从后面删除多余的
              const itemsToKeep = [];
              let keepCount = 0;
              // 保留前quantity个匹配的项
              targetSpace.goods.forEach((item) => {
                if (item.componentCode === code) {
                  if (keepCount < quantity) {
                    itemsToKeep.push(item);
                    keepCount++;
                  }
                  // 超过quantity的不添加到itemsToKeep，相当于删除
                } else {
                  itemsToKeep.push(item);
                }
              });
              targetSpace.goods = itemsToKeep;
            }
            // 如果quantity等于existingCount，不需要做任何操作
          });

          // 处理在data中不存在但在targetSpace.goods中存在的componentCode
          targetSpace.goods = targetSpace.goods.filter((item) => {
            return dataComponentCodeMap.has(item.componentCode);
          });

          // 添加新增的商品
          targetSpace.goods = [...targetSpace.goods, ...newGoods];
          console.log('targetSpace.goods', targetSpace.goods);
        }
        let productCodes = []; // productCodes是用于计算场景状态的入参
        // 添加选中的商品
        data.forEach((item) => {
          productCodes.push(item.componentCode);
        });
        // 将data转成跟左侧边栏相同的二维数组，添加进leftProductList
        const tempDataMap = {};
        data.forEach((item) => {
          const key = item.baseSubModuleId;
          if (!tempDataMap[key]) {
            tempDataMap[key] = [];
          }
          tempDataMap[key].push({ ...item });
        });
        const groupedData = Object.values(tempDataMap);
        // 如果leftProductList为空，直接用新数据填充
        if (leftProductList.value.length === 0) {
          leftProductList.value = groupedData.map((group) => {
            const [firstItem] = group;
            return {
              baseSubModuleId: firstItem.baseSubModuleId,
              subModuleName: firstItem.subModuleName,
              goodIds: group.map((i) => ({
                uuid: '',
                componentCode: i.componentCode,
                componentName: i.componentDesc,
                componentDesc: i.componentDesc,
                mainImage: i.mainImage,
                quantity: i.quantity,
              })),
            };
          });
        } else {
          // 合并或新增数据到 leftProductList
          groupedData.forEach((group) => {
            const [firstItem] = group;
            const existingIndex = leftProductList.value.findIndex(
              (s) => s.baseSubModuleId === firstItem.baseSubModuleId
            );
            const formattedItems = group.map((i) => ({
              uuid: '',
              componentCode: i.componentCode,
              componentName: i.componentDesc,
              componentDesc: i.componentDesc,
              mainImage: i.mainImage,
              quantity: i.quantity,
            }));
            if (existingIndex > -1) {
              // 合并数据
              formattedItems.forEach((newItem) => {
                const existingGoodIndex = leftProductList.value[existingIndex].goodIds.findIndex(
                  (good) => good.componentCode === newItem.componentCode
                );
                if (existingGoodIndex > -1) {
                  // 如果商品已存在，修改数量
                  leftProductList.value[existingIndex].goodIds[existingGoodIndex].quantity = newItem.quantity;
                  leftProductList.value[existingIndex].goodIds[existingGoodIndex].mainImage = newItem.mainImage;
                  leftProductList.value[existingIndex].goodIds[existingGoodIndex].componentName = newItem.componentDesc;
                  leftProductList.value[existingIndex].goodIds[existingGoodIndex].componentDesc = newItem.componentDesc;
                } else {
                  // 如果商品不存在，添加新商品
                  leftProductList.value[existingIndex].goodIds.push(newItem);
                }
              });
            } else {
              // 新增数据
              leftProductList.value.push({
                baseSubModuleId: firstItem.baseSubModuleId,
                subModuleName: firstItem.subModuleName,
                goodIds: formattedItems,
              });
            }
          });
        }
        // 检查并删除在data中不存在但在leftProductList中存在的项
        leftProductList.value.forEach((category, categoryIndex) => {
          // 获取当前分类在data中对应的所有componentCode
          const dataComponentCodes = data
            .filter((item) => item.baseSubModuleId === category.baseSubModuleId)
            .map((item) => item.componentCode);
          // 过滤掉在data中不存在的商品
          category.goodIds = category.goodIds.filter((good) => {
            return dataComponentCodes.includes(good.componentCode);
          });
          // 如果分类下没有商品了，标记为删除
          if (category.goodIds.length === 0) {
            leftProductList.value[categoryIndex] = null;
          }
        });
        // 移除空分类
        leftProductList.value = leftProductList.value.filter(Boolean);
        // 保持响应式更新
        leftProductList.value = [...leftProductList.value];
        const params = {
          productCodes: productCodes,
        };
        await _API_getListByMk(params).then((res) => {
          if (res?.code === '0') {
            sceneProgress.value = res.data.map((item) => {
              return {
                ...item,
                selected: selectSceneList.value.includes(item.marketingCode),
              };
            });
          }
        });
      }
      if (devices.value.length > 0) {
        devices.value.forEach((item) => {
          sceneProgress.value.forEach((s) => {
            if (s.marketingCode === item.marketingCode) {
              item.status = s.status;
            }
          });
          // 获取data中所有的componentCode，用于后续比较
          const dataComponentCodes = data.map((d) => d.componentCode);
          // 先添加新数据
          data.forEach((d) => {
            item.subModuleList.forEach((sub) => {
              if (sub.subModuleId.includes(d.baseSubModuleId)) {
                d.componentName = d.componentDesc;
                // 检查是否已存在相同的componentCode
                const exists = sub.productList.some((product) => product.componentCode === d.componentCode);
                if (!exists) {
                  sub.productList.push(d);
                }
              }
            });
          });

          // 删除在data中不存在但在devices.value的subModuleList中存在的项
          item.subModuleList.forEach((sub) => {
            // 过滤掉不在dataComponentCodes中的商品
            sub.productList = sub.productList.filter((product) => dataComponentCodes.includes(product.componentCode));
          });
        });
      }
    }
  }
  // 选择房间
  function selectRoom(room) {
    rooms.value.forEach((item) => {
      item.selected = item.roomId === room.roomId;
    });
    selectedRoomId.value = room.roomId;
    selectedRoom.value = room.name;
    devices.value = [];
    leftProductList.value = [];
    getRoomProductList();
  }

  function changeSceneSelected(scene) {
    scene.selected = !scene.selected;
    if (scene.selected) {
      editLeftAndSelectScene(scene);
      allSpaceProductList.value.forEach((item) => {
        if (item.spaceId === selectedRoomId.value) {
          item.scene.push({ sceneCode: scene.marketingCode, roomId: selectedRoomId.value });
        }
      });
    } else {
      devices.value = devices.value.filter((item) => item.marketingCode !== scene.marketingCode);
      selectSceneList.value = selectSceneList.value.filter((item) => item !== scene.marketingCode);
      allSpaceProductList.value.forEach((item) => {
        if (item.spaceId === selectedRoomId.value) {
          item.scene = item.scene.filter((i) => i.sceneCode !== scene.marketingCode);
        }
      });
    }
  }
  function editLeftAndSelectScene(scene) {
    selectSceneList.value.push(scene.marketingCode);
    const subModuleGoodMap = new Map();
    leftProductList.value.forEach((subModule) => {
      subModule.goodIds.forEach((goodId) => {
        const key = subModule.baseSubModuleId;
        if (!subModuleGoodMap.has(key)) {
          subModuleGoodMap.set(key, []);
        }
        // 查找对应的商品信息
        const good = leftProductList.value
          ?.flatMap((subModule) => subModule.goodIds)
          ?.find((good) => good.componentCode === goodId.componentCode);
        if (good) {
          subModuleGoodMap.get(key).push(good);
        }
      });
    });
    scene.essentials.forEach((item) => {
      item.forEach((sub) => {
        sub.productList = subModuleGoodMap.get(sub.subModuleId) || [];
      });
    });

    const subModuleList = scene?.essentials?.map((item) => item?.map((i) => i.subModuleName).join('/')) || [];

    const subModuleIds = scene?.essentials?.map((item) => item?.map((i) => i.subModuleId).join(',')) || [];

    const subProductList = scene?.essentials?.map((item) => item.flatMap((i) => i.productList)) || [];
    Object.assign(scene, {
      subModuleList,
      subModuleIds,
      subProductList,
    });
    scene.subModuleList = subModuleList.map((item, index) => ({
      productList: subProductList[index],
      subModuleName: item,
      subModuleId: subModuleIds[index],
    }));
    devices.value.push(scene);
  }
  function selectNextScene() {
    createQuotationOpen.value = false;
    const currentRoom = allSpaceProductList.value.find((item) => item.spaceId === selectedRoomId.value);
    // 如果当前选中的房间下scene长度为0，则不做处理
    if (currentRoom && currentRoom.scene.length === 0) {
      return;
    }
    // 当前房间scene长度不为0，寻找数组中下一个scene长度为0的房间
    const emptySceneRooms = allSpaceProductList.value.filter((item) => item.scene.length === 0);
    // 如果没有空场景房间，则从头开始找第一个房间
    if (emptySceneRooms.length === 0) {
      // 所有房间的scene都不是0，从头开始找第一个房间
      if (allSpaceProductList.value.length > 0) {
        const firstRoom = allSpaceProductList.value[0];
        selectRoom(rooms.value.find((room) => room.roomId === firstRoom.spaceId));
      }
      return;
    }
    // 找到当前房间在allSpaceProductList中的索引
    const currentIndex = allSpaceProductList.value.findIndex((item) => item.spaceId === selectedRoomId.value);
    // 从当前索引开始向后查找scene长度为0的房间
    let targetRoom = null;
    for (let i = currentIndex + 1; i < allSpaceProductList.value.length; i++) {
      if (allSpaceProductList.value[i].scene.length === 0) {
        targetRoom = allSpaceProductList.value[i];
        break;
      }
    }
    // 如果向后没找到，则从头开始找
    if (!targetRoom) {
      for (let i = 0; i < currentIndex; i++) {
        if (allSpaceProductList.value[i].scene.length === 0) {
          targetRoom = allSpaceProductList.value[i];
          break;
        }
      }
    }
    // 如果找到目标房间，执行相应操作，选中该房间
    if (targetRoom) {
      selectRoom(rooms.value.find((room) => room.roomId === targetRoom.spaceId));
    }
  }
  function createQuotation() {
    if (allSpaceProductList.value.length > 0) {
      emptySceneNum.value = allSpaceProductList.value.filter((item) => item.scene.length === 0).length;
      if (emptySceneNum.value > 0) {
        createQuotationOpen.value = true;
      } else {
        handleCreateQuotation();
      }
    }
  }
  /**
   * @description 生成报价单
   */
  function handleCreateQuotation() {
    createQuotationOpen.value = false;
    save('noMessage');
    emit('createQuotation', {
      action: 'quote',
    });
  }
  function formatStatus(status) {
    switch (status) {
      case 0:
        return '无设备';
      case 1:
        return '设备缺失';
      case 2:
        return '设备齐全';
      default:
        return '';
    }
  }
</script>

<template>
  <Transition name="fade" mode="out-in">
    <page-layout customClass="create-proposal-page">
      <template #header>
        <div class="create-proposal-header">
          <div class="title">智慧场景选择</div>
          <div class="header-buttons">
            <a-button class="header-button" size="small" @click="closePage">关闭</a-button>
            <a-button
              :disabled="buttonDisabled"
              :loading="confirmLoading"
              class="header-button"
              size="small"
              type="primary"
              @click="save">
              保存
            </a-button>
            <a-button
              :disabled="buttonDisabled"
              class="header-button"
              size="small"
              type="primary"
              @click="createQuotation">
              生成报价单
            </a-button>
          </div>
        </div>
      </template>
      <template #container>
        <div id="scene-container" class="container">
          <div class="top-section">
            <!-- 左侧户型图 -->
            <div class="floor-plan-section">
              <div class="floor-plan">
                <svg
                  width="9.8vw"
                  height="9.8vw"
                  class="floor-plan-svg"
                  :viewBox="viewBox"
                  xmlns="http://www.w3.org/2000/svg">
                  <g v-for="room in rooms" :key="room.roomId">
                    <polygon
                      :points="room.path"
                      :fill="room.selected ? '#E6D3A8' : '#f5f5f5'"
                      stroke="black"
                      stroke-width="0"
                      :id="room.roomId" />
                    <text
                      :x="room.center[0]"
                      :y="room.center[1]"
                      text-anchor="middle"
                      dominant-baseline="middle"
                      vector-effect="non-scaling-stroke"
                      :fill="room.selected ? '#99641F' : ''"
                      :font-size="room.selected ? 40 : 38">
                      {{ room.name }}
                    </text>
                  </g>
                </svg>
              </div>
            </div>
            <div class="room-scene-section">
              <div class="room-list-container" ref="roomListContainer">
                <Swiper
                  ref="swiperRef"
                  :modules="[Navigation, Mousewheel]"
                  :slides-per-view="'auto'"
                  :space-between="16"
                  :navigation="{
                    nextEl: '.swiper-button-next-custom',
                    prevEl: '.swiper-button-prev-custom',
                  }"
                  :mousewheel="{
                    forceToAxis: true,
                    sensitivity: 1,
                    releaseOnEdges: true,
                  }"
                  :grab-cursor="true"
                  :allow-touch-move="true"
                  :class="['room-list-swiper', showNavigation ? 'with-navigation' : '']"
                  @swiper="onSwiper"
                  @slideChange="onSlideChange">
                  <SwiperSlide v-for="room in rooms" :key="room.roomId" class="room-slide">
                    <div class="room-item" @click="selectRoom(room)" :class="{ 'room-selected': room.selected }">
                      <div
                        v-if="allSpaceProductList.find((item) => item.spaceId === room.roomId)?.scene.length > 0"
                        class="selected-tag">
                        已有场景
                      </div>
                      <div class="room-image">
                        <div class="room-svg">
                          <svg
                            width="5.4vw"
                            height="5.4vw"
                            class="room-svg-image"
                            :viewBox="room.viewBox"
                            xmlns="http://www.w3.org/2000/svg">
                            <polygon
                              :points="room.path"
                              :fill="room.selected ? '#E6D3A8' : '#D9D9D9'"
                              stroke="black"
                              stroke-width="0"
                              :id="room.roomId" />
                          </svg>
                        </div>
                        <div v-ellipsis-tooltip class="room-name">
                          {{ room.name }}
                        </div>
                      </div>
                    </div>
                  </SwiperSlide>

                  <!-- 自定义导航按钮 -->
                  <div v-show="showNavigation" class="swiper-button-prev-custom swiper-nav-button">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path
                        d="M10 12L6 8L10 4"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                    </svg>
                  </div>
                  <div v-show="showNavigation" class="swiper-button-next-custom swiper-nav-button">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path
                        d="M6 4L10 8L6 12"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                    </svg>
                  </div>
                </Swiper>
              </div>
              <div class="scene-progress" ref="sceneProgressContainer">
                <Swiper
                  ref="sceneProgressSwiperRef"
                  :modules="[Navigation, Mousewheel]"
                  :slides-per-view="'auto'"
                  :space-between="10"
                  :navigation="{
                    nextEl: '.scene-swiper-button-next-custom',
                    prevEl: '.scene-swiper-button-prev-custom',
                  }"
                  :mousewheel="{
                    forceToAxis: true,
                    sensitivity: 1,
                    releaseOnEdges: true,
                  }"
                  :grab-cursor="true"
                  :allow-touch-move="true"
                  @swiper="onSceneProgressSwiper"
                  :class="['scene-progress-swiper', showSceneProgressNavigation ? 'with-navigation' : '']">
                  <SwiperSlide
                    v-for="scene in sceneProgress"
                    :key="scene.id"
                    class="scene-slide">
                    <div
                      class="progress-item"
                      :class="scene.selected ? 'scene-selected' : ''"
                      @click="changeSceneSelected(scene)">
                      <div :class="['select-item', scene.selected ? 'selected' : '']"></div>
                      <div class="scene-top">
                        <div v-ellipsis-tooltip class="scene-name">{{ scene.marketingName }}</div>
                      </div>
                      <div
                        class="device"
                        :class="
                          scene.status === 0 ? 'no-device' : scene.status === 1 ? 'lack-device' : 'complete-device'
                        ">
                        {{ formatStatus(scene.status) }}
                      </div>
                    </div>
                  </SwiperSlide>

                  <!-- 自定义导航按钮 -->
                  <div v-show="showSceneProgressNavigation" class="scene-swiper-button-prev-custom scene-swiper-nav-button">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path
                        d="M10 12L6 8L10 4"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                    </svg>
                  </div>
                  <div v-show="showSceneProgressNavigation" class="scene-swiper-button-next-custom scene-swiper-nav-button">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path
                        d="M6 4L10 8L6 12"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                    </svg>
                  </div>
                </Swiper>
              </div>
            </div>
          </div>
          <div class="bottom-section" ref="bottomSectionRef" :style="{height: componentHeight ? `${componentHeight}px` : 'calc(100% - 324px)'}">
            <RoomProductList
              :selectedRoom="selectedRoom"
              :sub-product-list="leftProductList"
              :containerHeight="componentHeight"
              @addProduct="addProduct"
              @deleteProduct="deleteProduct"
              @changeQuantity="changeQuantity"></RoomProductList>
            <SelectedScene
              :devices="devices"
              :containerHeight="componentHeight"
              @addProduct="addProduct"></SelectedScene>
          </div>
          <add-product-model
            v-model="addProductVisible"
            :selectedSubModule="selectedSubModule"
            :title="addProductTitle"
            :showFilter="addProductTitle === '添加商品'"
            :selectComponentCodes="selectComponentCodes"
            @close="addProductVisible = false"
            @select-product="addProductConfirm"></add-product-model>
          <a-modal
            v-model:open="closeAndSaveOpen"
            title=""
            :maskClosable="false"
            :keyboard="false"
            :closable="false"
            width="420px"
            @cancel="handleCancel">
            <div class="close-container">
              <Icon icon="synAttention" :size="24" class="error-icon"></Icon>
              <div>
                <div class="tip">提示</div>
                <div v-if="emptySceneNum > 0">
                  您还有
                  <span class="empty-num">{{ emptySceneNum }}个空间</span>
                  未设置，是否关闭智慧场景
                </div>
                <div v-if="emptySceneNum === 0">您确认要关闭智慧场景?</div>
              </div>
            </div>

            <template #footer>
              <div class="footer-buttons">
                <a-button v-if="emptySceneNum > 0" key="back" @click="handleCancel">取 消</a-button>
                <a-button v-if="emptySceneNum === 0" key="back" @click="handleClose">直接关闭</a-button>
                <a-button key="submit" type="primary" :loading="confirmLoading" @click="handleCloseAndSave">
                  保存并关闭
                </a-button>
              </div>
            </template>
          </a-modal>
          <a-modal
            v-model:open="createQuotationOpen"
            title=""
            :maskClosable="false"
            :keyboard="false"
            width="420px"
            @cancel="selectNextScene">
            <div class="close-container">
              <Icon icon="synAttention" :size="24" class="error-icon"></Icon>
              <div>
                <div class="tip">提示</div>
                <div>
                  您还有
                  <span class="empty-num">{{ emptySceneNum }}个空间</span>
                  未设置，是否继续设置剩下空间
                </div>
              </div>
            </div>
            <template #footer>
              <a-button
                class="create-quo"
                key="back"
                type="text"
                :loading="confirmLoading"
                @click="handleCreateQuotation">
                直接生成报价单
              </a-button>
              <a-button key="submit" type="primary" @click="selectNextScene">是，继续</a-button>
            </template>
          </a-modal>
        </div>
      </template>
    </page-layout>
  </Transition>
</template>

<style scoped lang="stylus">
    .create-proposal-page
      position fixed
      z-index 20;
      top 0
      left 0
      width 100%
      height 100%
      background-color #ffffff
      padding 0
      .create-proposal-header {
        display flex;
        justify-content space-between;
        padding 16px 20px;
        height: 64px;
        box-shadow: 0px -1px 0px 0px #F0F0F0 inset;
        .title {
          color: rgba(0, 0, 0, 0.85);
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 32px;
        }
        .header-button {
          margin-left 8px;
          height 32px;
          padding 5px 16px;
        }
      }

    .container {
      width: 100%;
      height: 100%
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .top-section {
      display: flex;
      width: 100%;
      padding: 20px 10px 20px 20px;
    }

    .bottom-section {
      display: flex;
      width: 100%;
      padding: 20px;
      flex: 1;
      background-color: #E8EEF1;
    }

    .floor-plan-section {
      width: 15vw;
      height 15vw;
      min-width: 193px;
      min-height 193px;
      margin-right: 20px;
      border: 0.3vw solid #F0F0F0
      border-radius: 16px;
    }
    .floor-plan-svg {
      min-width: 165px;
      min-height 165px;
    }

    .room-scene-section {
      flex: 1;
      width: calc(100% - 213px);
      min-width: 500px;
      display: flex;
      flex-direction: column;
    }

    .floor-plan {
      width: 14.4vw;
      height: 14.4vw;
      min-width: 185px;
      min-height: 185px;
      border-radius: 16px;
      overflow: hidden;
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .room-list-container {
      width 100%
      border-radius: 4px;
      padding: 0;
      position: relative;
      overflow: hidden;
    }

    .room-slide {
      width: auto !important;
      flex-shrink: 0;
    }

    // 自定义导航按钮样式
    .swiper-nav-button {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 32px;
      height: 32px;
      background: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 15; // 提高z-index确保显示在遮罩层之上
      color: #666;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &:hover {
        background: #f5f5f5;
        border-color: var(--opn--primary-color);
        color: var(--opn--primary-color);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &.swiper-button-disabled {
        opacity: 0.3;
        cursor: not-allowed;

        &:hover {
          background: #fff;
          border-color: #d9d9d9;
          color: #666;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .swiper-button-prev-custom {
      left: 9px; // 在50px padding中居中：(50-32)/2 = 9px
    }

    .swiper-button-next-custom {
      right: 9px; // 在50px padding中居中：(50-32)/2 = 9px
    }
  :deep(.swiper) {
    overflow: visible; // 允许溢出，以便导航按钮可以显示在外部
  }
  .room-list-swiper {
    width: 100%;
    padding: 0 0 16px 0; // 默认底部padding为16px
    transition: padding 0.3s ease;
    color: rgba(0,0,0,0.85);
    
    &.with-navigation {
      padding: 0 60px 16px 60px; // 当导航按钮显示时添加左右 padding，底部padding为0
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 50px;
        height: 100%;
        background: #fff;
        z-index: 5;
        pointer-events: none;
      }
      
      &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        width: 50px;
        height: 100%;
        background: #fff;
        z-index: 5;
        pointer-events: none;
      }
    }
    
    .swiper-wrapper {
      display: flex;
      align-items: center;
    }
    .room-selected {
      border: 2px solid var(--opn--primary-color)!important;
      box-shadow: 0 2px 8px 0 rgba(230, 211, 168, 0.40) !important;
      color: var(--opn--primary-color)!important;
      background-color: var(--blue-1)!important;
      &::before {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background-color: var(--blue-1);
        z-index: 1;
      }
      &::after {
        content: '';
        position: absolute;
        bottom: -11px;
        left: 50%;
        width: 20px;
        height: 20px;
        background-color: var(--blue-1);
        z-index: 0;
        border-bottom: 2px solid var(--opn--primary-color);
        border-right: 2px solid var(--opn--primary-color);
        transform-origin: center;
        transform: translateX(-50%) rotate(45deg);
      }
    }
    .room-item {
      color: rgba(0, 0, 0, 0.85);
      text-align: center;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px; /* 150% */
      cursor: pointer;
      border-radius 10px;
      padding 0.4vw 0.9vw;
      background-color #F8F7F8;
      border 2px solid var(--card-hover-gb-color);
      width 8vw;
      position relative;
      min-width 86px!important;
      &:hover {
        box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
      }
      .selected-tag {
        position absolute;
        top 4px;
        left 4px;
        padding: 1px 8px;
        align-items: center;
        gap: 3px;
        border-radius: 6px
        background: rgba(0, 0, 0, 0.45)
        color: #FFF;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
    }

    .room-svg {
      width 6.2vw;
      height 6.2vw;
      padding 0.4vw;
      min-width 64px;
      min-height 64px;
    }
    
    .room-svg-image{
      min-width 50px;
      min-height 50px;
    }

    .room-name {
      text-align: center;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      margin-top: 4px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

    .scene-progress {
      width: 100%;
      position: relative;
      padding: 13px 0;
    }

    .scene-progress-swiper {
      width: 100%;
      padding: 0; // 默认无 padding
      transition: padding 0.3s ease;
      overflow: hidden;
      user-select: none; // 防止文本选择干扰拖拽
      cursor: grab;
      
      &:active {
        cursor: grabbing;
      }
      
      &.with-navigation {
        padding: 0 60px; // 当导航按钮显示时添加左右 padding
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          width: 50px;
          height: 100%;
          background: #fff;
          z-index: 5;
          pointer-events: none;
        }
        
        &::after {
          content: '';
          position: absolute;
          right: 0;
          top: 0;
          width: 50px;
          height: 100%;
          background: #fff;
          z-index: 5;
          pointer-events: none;
        }
      }
      
      .swiper-wrapper {
        display: flex;
        align-items: center;
        cursor: grab;
        
        &:active {
          cursor: grabbing;
        }
      }
      
      .swiper-slide {
        cursor: grab;
        
        &:active {
          cursor: grabbing;
        }
      }
    }

    .scene-slide {
      width: auto !important;
      flex-shrink: 0;
      user-select: none; // 防止文本选择干扰拖拽
    }

    .scene-swiper-nav-button {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 32px;
      height: 32px;
      background: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 15; // 提高 z-index 确保显示在遮罩层之上
      color: #666;
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--opn--primary-color);
        color: var(--opn--primary-color);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      &.scene-swiper-button-prev-custom {
        left: 9px; // 在 50px padding 中居中：(50-32)/2 = 9px
      }

      &.scene-swiper-button-next-custom {
        right: 9px; // 在 50px padding 中居中：(50-32)/2 = 9px
      }
    }
  .scene-selected {
    border: 1px solid var(--opn--primary-color)!important;
  }
  .progress-item {
    width: 120px;
    border-radius: 8px;
    border: 1px solid #D9D9D9;
    padding: 12px;
    background: #FFF;
    position relative;
    cursor pointer;
    user-select: none; // 防止文本选择
    touch-action: pan-x; // 允许水平拖拽
    pointer-events: auto; // 确保可以接收事件
    
    &:hover {
      border: 1px solid  var(--opn--hover-color);
    }
    .select-item {
      position: absolute
      top: 14px
      right: 12px
      width: 16px
      height: 16px
      border-radius: 4px
      border: 1px solid var(--case-item-border-color)
      background: #fff
      box-sizing: border-box
      z-index: 1
    }
    .selected {
      background-color: var(--opn--primary-color)
      border: none
      background-image: url('@/assets/images/selected-icon.png');
      background-size: cover
    }
    .scene-top {
      display: flex;
      justify-content: space-between;
      .scene-name {
        color: rgba(0, 0, 0, 0.85);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
        width 75px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        margin-bottom 4px;
      }
    }
  }

    .device {
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;

      &.no-device {
        color: var(--opn-color-required);
      }
      &.lack-device {
        color: #FA8C16;
      }
      &.complete-device {
        color: #52C41A;
      }
    }
  .close-container {
    padding 14px 8px 0;
    color: rgba(0, 0, 0, 0.85);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    display: flex;
    .empty-num {
      color: var(--opn-color-required);
    }
    .error-icon {
      color: var(--opn-color-required);
      margin-right 16px;
      position relative;
      top 2px;
    }
    .tip {
      color:rgba(0, 0, 0, 0.85);
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      margin-bottom 2px;
    }
  }
  .footer-buttons{
    margin-top: 24px;
    margin-bottom: 4px;
  }
  .create-quo {
    border: 1px solid var(--opn--primary-color);
    background: #FFF;
    color: var(--opn--primary-color);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    &:hover{
      background: #fff;
      color: var(--opn--primary-color);
    }
  }

  // 媒体查询：屏幕宽度低于1024px时的样式调整
  @media (max-width: 1023px) {
    .room-name {
      font-size: 14px !important;
    }
    
    .scene-name {
      font-size: 12px !important;
    }
    
    .scene-progress {
      padding-top: 8px !important;
    }
    
    .device {
      font-size: 12px !important;
    }
  }
</style>
