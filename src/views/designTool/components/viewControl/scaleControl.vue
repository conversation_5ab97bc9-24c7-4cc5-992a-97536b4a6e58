<script setup>
  import { ref } from 'vue';
  import Icon from '@/components/icon/icon.vue';
  import { useCaseDataStore } from '@/store/modules/caseDataStore';
  const caseDataStore = useCaseDataStore();
  const emit = defineEmits(['onAction']);
  const scaleValue = ref(caseDataStore.viewScale * 100);
  const step = ref(1);
  const min = 10;
  const max = 100;

  caseDataStore.$subscribe((_, state) => {
    scaleValue.value = Number((state.viewScale * 100).toFixed(0));
  });
  /**
   * @description 修改缩放比
   * @param type
   */
  function handleChangeNum(type) {
    if (type === 1) {
      scaleValue.value = scaleValue.value === max ? max : scaleValue.value + step.value;
    } else {
      scaleValue.value = scaleValue.value === min ? min : scaleValue.value - step.value;
    }
    handleChange(scaleValue.value);
  }

  /**
   * @description input失去焦点修改缩放比
   * @param e
   */
  function handleBlurChange(e) {
    let value = e?.target?.value;
    let scaleValue = Number(value) > 100 ? 100 : Number(value);
    emitAction(Number(scaleValue));
  }
  /**
   * @description 回车修改缩放比
   * @param value
   */
  function handleChange(value) {
    if (!value) {
      return;
    }
    emitAction(value);
  }

  /**
   * @description 发送emit事件
   * @param value
   */
  function emitAction(value) {
    emit('onAction', {
      action: 'viewScale',
      payload: value / 100,
    });
  }
</script>

<template>
  <div class="view-scale-wrap">
    <Icon icon="synZoomOut" :size="20" pointer @click="handleChangeNum(0)"></Icon>
    <a-input-number
      class="view-scale-input"
      :min="min"
      :max="max"
      :step="step"
      size="small"
      v-model:value="scaleValue"
      :controls="false"
      @blur="handleBlurChange"
      @pressEnter="handleBlurChange">
      <template #addonAfter>%</template>
    </a-input-number>
    <Icon icon="synZoomIn" :size="20" pointer @click="handleChangeNum(1)"></Icon>
  </div>
</template>

<style scoped lang="stylus">
  .view-scale-wrap
    background #ffffff
    border-radius 6px
    height 36px
    padding 6px 12px
    width 160px
    display flex
    align-items center
    justify-content center
    border-right none !important
    gap 8px
  .view-scale-input
    width 80px
    border-right none !important
  :deep(.ant-input-number-wrapper) {

    &:hover {
      .ant-input-number{
        border-color var(--opn--hover-color)
      }
      .ant-input-number-group-addon {
        border-color var(--opn--hover-color)
      }
    }
    .ant-input-number:focus {
      & + .ant-input-number-group-addon {
        border-color: var(--opn--hover-color);
      }
    }
  }
  :deep(.ant-input-number)
    border-right none
    border-radius 6px 0 0 6px
  :deep(.ant-input-number-input)
    padding 0 0 0 8px !important
  :deep(.ant-input-number-group-addon)
    background #fff
    width 12px
    padding 0
    padding-right 8px
    padding-left 4px
    border-radius 0 6px 6px 0
    color rgba(0,0,0,0.25)
</style>
