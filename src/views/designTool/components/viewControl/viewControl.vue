<script setup>
  import Icon from '@/components/icon/icon.vue';
  import { createViewControl, floorOpacityList } from './viewControlConfig';
  import { computed, onMounted, ref, watch } from 'vue';
  import { useDictStore } from '@/store/modules/dictStore';
  import { SYSTEM_LIST } from '@/config/dictKeys';
  import { useCaseStatusStore } from '@/store/modules/caseStatusStore';
  import { useCaseDataStore } from '@/store/modules/caseDataStore';
  import { useModal } from '@/hooks/useModal';
  const dictStore = useDictStore();
  const emit = defineEmits(['onAction']);
  const viewControlBtn = ref([]);
  const systemList = ref([]);
  const openStates = ref({});
  const space = ref(true);
  const annotation = ref(false);
  const system = ref([]);
  const selectedKeys = ref([0.5]);
  const caseStatusStore = useCaseStatusStore();
  const hasIntelligentGoods = ref(!caseStatusStore.statusMap?.hasIntelligentGoods);
  const disabledAnnotation = ref(true);
  const caseDataStore = useCaseDataStore();

  const disabledFloorImageBtn = computed(() => {
    return !caseStatusStore.statusMap?.hasBackgroundImage;
  });

  watch(
    () => hasIntelligentGoods.value,
    (value) => {
      if (!value) {
        system.value = systemList.value.map((item) => item.value);
      } else {
        system.value = [];
      }
    }
  );
  /**
   * @description 删除底图
   * @param key
   */
  function handleClick(key) {
    if (key === 'deleteFloorImage') {
      useModal({
        type: 'confirm',
        title: '提示',
        content: '您确认要删除底图吗?',
        closable: true,
        maskClosable: true,
        hideCancel: false,
        cancelText: '取消',
        onCancel: () => {},
        onOk: () => {
          emit('onAction', {
            action: key,
          });
        },
      });
      return;
    }
    emit('onAction', {
      action: key,
    });
  }

  /**
   * @description 修改透明度
   * @param item
   */
  function changeOpacity(item) {
    selectedKeys.value = [item.value];
    caseDataStore.setOpacity(item.value);
    emit('onAction', {
      action: 'setFloorOpacity',
      payload: item.value,
    });
  }

  function handleSystemChange(e) {
    let payload = {
      action: null,
      payload: null,
    };
    if (e === 'Space') {
      payload = {
        action: 'changeSpaceStatue',
        payload: space.value,
      };
    }
    if (e === 'Annotation') {
      payload = {
        action: 'changeAnnotationStatue',
        payload: annotation.value,
      };
    }
    emit('onAction', {
      ...payload,
    });
  }

  /**
   * @description 处理菜单点击事件
   * @param action
   */
  function handleMenuClick(action) {
    // 关闭所有菜单
    Object.keys(openStates.value).forEach((key) => {
      openStates.value[key] = false;
    });

    // 只有显示设置菜单点击时设置为true
    openStates.value[action] = action === 'viewControl';
  }

  /**
   * @description 修改显示的系统列表
   * @param value
   */
  function changeShowSystem(value) {
    emit('onAction', {
      action: 'changeSystemDeviceStatue',
      payload: value,
    });
  }

  async function initPage() {
    let arr = await createViewControl();
    viewControlBtn.value = arr.map((item) => {
      if (item.action === 'viewControl') {
        item.disabled = false;
      }
      if (item.action === 'floorImageSet') {
        item.disabled = disabledFloorImageBtn.value;
      }
      return item;
    });

    // 初始化每个菜单的打开状态
    viewControlBtn.value.forEach((menu) => {
      openStates.value[menu.action] = false;
    });
  }

  caseStatusStore.$subscribe((_, state) => {
    hasIntelligentGoods.value = !state.statusMap.hasIntelligentGoods;
    disabledAnnotation.value = !state.statusMap.hasAnnotation; // 没有施工标注直接禁用
    annotation.value = !disabledAnnotation.value;
    initPage();
  });

  /**
   * @description 透明度设置
   * @param menu
   * @param open
   */
  function openChange(menu, open) {
    if (menu.action === 'floorImageSet' && open) {
      selectedKeys.value = [caseDataStore.opacity];
    }
  }

  onMounted(async () => {
    systemList.value = await dictStore.getDictStore(SYSTEM_LIST);
    await initPage();
  });
</script>

<template>
  <a-dropdown
    placement="top"
    v-for="menu in viewControlBtn"
    :disabled="menu.disabled"
    :key="menu.label"
    @open-change="(open) => openChange(menu, open)"
    v-model:open="openStates[menu.action]"
    @click.prevent="() => handleMenuClick(menu.action)"
    trigger="click">
    <div :class="['view-control-item', menu.disabled ? 'disabled' : '']">
      <Icon :icon="menu.icon"></Icon>
      <div class="label">{{ menu.label }}</div>
    </div>
    <template #overlay>
      <template v-if="menu.action === 'floorImageSet'">
        <a-menu v-model:selectedKeys="selectedKeys">
          <a-sub-menu title="底图透明度">
            <a-menu-item v-for="opItem in floorOpacityList" :key="opItem.value" @click="changeOpacity(opItem)">
              {{ opItem.label }}
            </a-menu-item>
          </a-sub-menu>
          <a-menu-item key="deleteFloorImage" @click="handleClick('deleteFloorImage')">删除底图</a-menu-item>
        </a-menu>
      </template>
      <template v-else>
        <a-menu>
          <a-sub-menu title="智慧系统" :disabled="hasIntelligentGoods">
            <a-checkbox-group
              v-model:value="system"
              style="
                display: flex;
                flex-direction: column;
                width: 96px;
                padding: 0 8px;
                align-items: flex-start;
                gap: 5px;
              "
              :options="systemList"
              @change="changeShowSystem">
              <a-menu-item></a-menu-item>
            </a-checkbox-group>
          </a-sub-menu>
          <a-menu-item>
            <a-checkbox
              v-model:checked="annotation"
              :disabled="disabledAnnotation"
              @change="(value) => handleSystemChange('Annotation', value)">
              施工标注
            </a-checkbox>
          </a-menu-item>
          <a-menu-item>
            <a-checkbox v-model:checked="space" @change="(value) => handleSystemChange('Space', value)">
              房间名称
            </a-checkbox>
          </a-menu-item>
        </a-menu>
      </template>
    </template>
  </a-dropdown>
</template>

<style scoped lang="stylus">
  .view-control-item
    height 36px
    width 102px
    padding 8px 12px
    border 1px solid transparent
    background #ffffff
    border-radius 6px
    display flex
    flex-direction row
    align-items center
    gap 8px
    cursor pointer;
    &:hover:not(.disabled)
      color #CCA054
      .label
        color #CCA054
    &.ant-dropdown-open
      color var(--opn--primary-color)
      border 1px solid #CCA054
      .label
        color var(--opn--primary-color)
    &.disabled
      opacity 0.4
    .label
      @extends .content-small
      color rgba(0,0,0,0.85)
  .view-control-group
    width 100%
    display flex
    flex-direction column
    gap 10px
    padding 10px 5px
    user-select none
  .set-floor-image
    display flex
    flex-direction row
    align-items center
    justify-content space-between
</style>
