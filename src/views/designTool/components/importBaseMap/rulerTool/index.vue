<template>
  <div class="ruler-tool-container">
    <div class="container">
      <div v-show="!imageUrl" class="uploader-overlay">
        <Uploader
          ref="uploaderRef"
          :max="1"
          width="100%"
          height="454px"
          @onChange="handleUploadChange"
        />
      </div>
      <div class="canvas-wrapper" :class="{ 'active': !!imageUrl }" ref="canvasWrapperRef" @wheel.prevent="handleWheel" @mousedown="handlePanStart">
        <canvas ref="canvasRef"></canvas>

        <!-- 图片处理loading -->
        <div v-if="imageProcessing" class="image-processing-overlay">
          <a-spin size="large">
            <template #indicator>
              <div class="custom-loading-indicator">
                <div class="loading-circle"></div>
              </div>
            </template>
          </a-spin>
          <div class="loading-text">正在处理图片...</div>
        </div>
        <!-- 操作提示 -->
        <div v-if="ruler.visible && !ruler.dragging && showTooltip" class="ruler-tooltip">
          <div class="tooltip-content">
            拖拽蓝色手柄调整比例尺长度，拖拽中间区域移动位置
            <button class="tooltip-close" @click="hideTooltip">×</button>
          </div>
        </div>

        <!-- 比例尺作为一个独立的SVG层，覆盖在Canvas之上 -->
        <div class="ruler-container" v-if="ruler.visible">
          <svg class="ruler-svg">
            <g :transform="rulerScreenTransform">
              <!-- 尺寸辅助线 -->
              <g stroke="#D9D9D9" stroke-width="1" fill="rgba(0, 0, 0, 0.15)">
                <line x1="8" y1="13" x2="8" y2="27" />
                <line :x1="rulerScreenLength - 8" y1="13" :x2="rulerScreenLength - 8" y2="27" />
                <line x1="8" y1="22" :x2="rulerScreenLength - 8" y2="22" />
                <!-- 交叉标记 -->
                <g transform="translate(8, 22)">
                  <line x1="-3" y1="-3" x2="3" y2="3" />
                  <line x1="-3" y1="3" x2="3" y2="-3" />
                </g>
                <g :transform="`translate(${rulerScreenLength - 8}, 22)`">
                  <line x1="-3" y1="-3" x2="3" y2="3" />
                  <line x1="-3" y1="3" x2="3" y2="-3" />
                </g>
              </g>

              <!-- 比例尺主体 -->
              <rect
                x="9"
                y="-11"
                :width="rulerScreenLength - 18"
                height="22"
                fill="#1890FF80"
                rx="2"
                ry="2"
                class="ruler-line"
                :class="{ 'dragging': ruler.dragging && ruler.dragType === 'move' }"
                @mousedown.stop="handleRulerMouseDown($event, 'move')" />

              <!-- 刻度 -->
              <g stroke="rgba(255, 255, 255, 0.8)" stroke-width="1.5">
                <line
                  v-for="tick in rulerTicks"
                  :key="tick.x"
                  :x1="tick.x"
                  :y1="-11"
                  :x2="tick.x"
                  :y2="-11 + tick.height" />
              </g>

              <!-- 两端封边 -->
              <rect x="7" y="-18" width="2" height="36" fill="#096DD9" />
              <rect :x="rulerScreenLength - 9" y="-18" width="2" height="36" fill="#096DD9" />

              <!-- 操作手柄 -->
              <circle
                cx="8"
                cy="0"
                r="8"
                fill="#FFFFFF"
                stroke="#096DD9"
                stroke-width="2"
                class="ruler-handle"
                @mousedown.stop="handleRulerMouseDown($event, 'p1')" />
              <circle
                :cx="rulerScreenLength - 8"
                cy="0"
                r="8"
                fill="#FFFFFF"
                stroke="#096DD9"
                stroke-width="2"
                class="ruler-handle"
                @mousedown.stop="handleRulerMouseDown($event, 'p2')" />
            </g>
            <!-- 输入框作为一个独立的、不旋转的图层 -->
            <g :transform="inputGroupTransform">
              <foreignObject width="120" height="40">
                <div class="ruler-input-wrapper"
                     :class="{
                       'focused': inputState.isFocused,
                       'over-max': inputState.isOverMax
                     }"
                     xmlns="http://www.w3.org/1999/xhtml">
                  <input
                    type="number"
                    v-model="ruler.value"
                    min="100"
                    :max="props.maxValue"
                    placeholder="实际尺寸"
                    @mousedown.stop
                    @keydown.enter="$event.target.blur()"
                    @focus="handleInputFocus"
                    @blur="handleInputBlur"
                    @input="handleInputChange" />
                  <span>mm</span>
                </div>
              </foreignObject>
            </g>
          </svg>
        </div>
        <!-- 图片操作按钮 -->
        <div v-if="imageUrl" class="image-actions">
          <div class="action-btn preview-btn" @click="handlePreview" title="预览图片">
            <Icon icon="synEyeOpen" color="#ffffff" :size="18" pointer></Icon>
            <span>预览</span>
          </div>
          <div v-if="showUpload" class="action-btn delete-btn" @click="handleDelete" title="删除图片">
            <Icon icon="synDelete" color="#ffffff" :size="18" pointer></Icon>
            <span>删除</span>
          </div>
          <div v-if="!showUpload && imageUrl" class="action-btn delete-btn" @click="handleClose" title="返回">
            <Icon icon="synRefresh" color="#ffffff" :size="18" pointer></Icon>
            <span>重新选择</span>
          </div>
        </div>
      </div>

      <!-- 图片预览 -->
      <a-image
        v-if="imageUrl"
        :src="imageUrl"
        style="display: none;"
        ref="previewImageRef"
        :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
      />
    </div>
    <div v-if="props.showUpload" class="tip">只支持.png .jpg格式</div>
  </div>

</template>

<script setup>
  import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue';
  import Icon from '@/components/icon/icon.vue';
  import Uploader from '@/components/upload/uploader.vue';
  /****
   * 比例尺计算公式:
   * pixelRate =  pixelLength / scale / worldLength;
   * 说明:
   * 1. pixelLength：尺子的像素长度；
   * 2. scale：canvas画布的缩放比例；
   * 3. worldLength：用户输入的长度；
   *
   ***/
  const emit = defineEmits(['change', 'close']);

  const props = defineProps({
    showUpload: {
      type: Boolean,
      default: true,
    },
    snapAngleThreshold: {
      type: Number,
      default: 10,
    },
    imageUrl: {
      type: String,
      default: '',
    },
    maxValue: {
      type: Number,
      default: 10000,
    },
    defaultRulerLength: {
      type: Number,
      default: 100,
    },
  });

  const imageUrl = ref('');
  const uploaderRef = ref(null);
  const canvasWrapperRef = ref(null);
  const canvasRef = ref(null);
  const ctx = ref(null);
  const image = ref(null); // 用于存储Image对象
  const previewImageRef = ref(null); // 预览图片组件引用

  // 错误状态管理
  const loading = ref(false);
  const imageProcessing = ref(false); // 图片处理中的状态
  const error = ref('');
  const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  // 提示状态管理
  const showTooltip = ref(true);
  let tooltipTimer = null;

  // 组件卸载标志和清理函数管理
  const isUnmounted = ref(false);
  const cleanupFunctions = new Set();

  // 预览状态管理
  const previewVisible = ref(false);

  const view = reactive({
    scale: 1,
    x: 0,
    y: 0,
  });

  // 缓存初始缩放比例，用于pixelRate计算
  const initialScale = ref(1);

  // 输入框状态管理
  const inputState = reactive({
    isFocused: false,
    isOverMax: false,
  });

  const ruler = reactive({
    visible: false,
    p1: { x: 100, y: 100 },
    p2: { x: 200, y: 100 },
    value: null, // 改为 null，支持空值状态
    dragging: false,
    dragType: '',
    startX: 0,
    startY: 0,
    startP1: { x: 0, y: 0 },
    startP2: { x: 0, y: 0 },
    snapping: false, // 角度吸附动画状态
  });

  const rulerLength = computed(() => {
    const dx = ruler.p2.x - ruler.p1.x;
    const dy = ruler.p2.y - ruler.p1.y;
    return Math.sqrt(dx * dx + dy * dy);
  });

  const rulerAngle = computed(() => {
    const dx = ruler.p2.x - ruler.p1.x;
    const dy = ruler.p2.y - ruler.p1.y;
    return Math.atan2(dy, dx) * (180 / Math.PI);
  });

  const pixelRate = computed(() => {
    // 增强空值处理：检查 null、undefined、0、负数、NaN
    const rulerValue = ruler.value;
    const lengthValue = rulerLength.value;
    if (rulerValue == null || rulerValue <= 0 || isNaN(rulerValue) ||
        lengthValue == null || lengthValue <= 0 || isNaN(lengthValue)) {
      return 0;
    }
    // 正确的公式: pixelRate = pixelLength / worldLength * 10 因为设计工具是cm单位
    // pixelLength 应该基于初始缩放比例计算，不受用户后续缩放影响
    // worldLength 是用户输入的真实世界长度
    return (lengthValue / rulerValue) * 10;
  });

  // 新增：比例尺在屏幕上的像素长度
  const rulerScreenLength = computed(() => rulerLength.value * view.scale);

  // 比例尺的变换，只包含平移和旋转，不包含缩放
  const rulerScreenTransform = computed(() => {
    const screenP1X = ruler.p1.x * view.scale + view.x;
    const screenP1Y = ruler.p1.y * view.scale + view.y;
    return `translate(${screenP1X}, ${screenP1Y}) rotate(${rulerAngle.value})`;
  });

  const inputGroupTransform = computed(() => {
    // 计算比例尺在屏幕上的中心点
    const screenCenterX = ((ruler.p1.x + ruler.p2.x) / 2) * view.scale + view.x;
    const screenCenterY = ((ruler.p1.y + ruler.p2.y) / 2) * view.scale + view.y;

    // 将输入框（120px宽）水平居中，并放置在比例尺中心线下方32px处
    const verticalOffset = 32;
    return `translate(${screenCenterX - 60}, ${screenCenterY + verticalOffset})`;
  });

  const rulerTicks = computed(() => {
    const ticks = [];
    const length = rulerScreenLength.value; // 使用屏幕长度进行计算

    // 增强空值和边界条件处理
    if (length == null || isNaN(length) || length < 2) {
      return [];
    }

    const minorTickSpacing = 12;
    const majorTickMultiplier = 5;
    const majorTickSpacing = minorTickSpacing * majorTickMultiplier;

    for (let i = 0; i <= length; i += minorTickSpacing) {
      if (i > length) continue;
      const isMajor = i % majorTickSpacing === 0;
      ticks.push({
        x: i,
        height: isMajor ? 15 : 8,
      });
    }
    return ticks;
  });

  // 统一监听所有会影响 pixelRate 的因素，并在值变化时触发 change 事件
  watch(
    [() => ruler.p1, () => ruler.p2, () => ruler.value],
    () => {
      // 增强空值处理：确保在有图片且有效数值的情况下才发出事件
      const value = parseFloat(`${ruler.value}`);
      if (imageUrl.value && value > 0 && value < props.maxValue) {
        emit('change', {
          pixelRate: pixelRate.value || 0,
          imageUrl: imageUrl.value,
        });
      } else {
        emit('change', {
          pixelRate: 0,
          imageUrl: '',
        });
      }
    },
    { deep: true }
  );

  // 核心绘图函数
  const draw = () => {
    if (!ctx.value || !image.value || !canvasRef.value) return;
    const canvas = canvasRef.value;
    const context = ctx.value;

    // 自动调整Canvas的物理尺寸以匹配其CSS尺寸，防止模糊
    const { clientWidth, clientHeight } = canvas;
    if (canvas.width !== clientWidth || canvas.height !== clientHeight) {
      canvas.width = clientWidth;
      canvas.height = clientHeight;
    }

    context.clearRect(0, 0, canvas.width, canvas.height);
    context.save();
    context.translate(view.x, view.y);
    context.scale(view.scale, view.scale);
    context.drawImage(image.value, 0, 0);
    context.restore();
  };

  // 创建安全的延时执行函数
  const createSafeTimeout = (callback, delay) => {
    const timeoutId = setTimeout(() => {
      // 检查组件是否已卸载
      if (!isUnmounted.value) {
        callback();
      }
      // 从清理函数集合中移除
      cleanupFunctions.delete(clearFn);
    }, delay);

    // 创建清理函数
    const clearFn = () => {
      clearTimeout(timeoutId);
      cleanupFunctions.delete(clearFn);
    };

    // 添加到清理函数集合
    cleanupFunctions.add(clearFn);

    return clearFn;
  };

  const loadImage = (url) => {
    if (!url) return;

    loading.value = true;
    imageProcessing.value = true; // 开始图片处理
    error.value = '';

    const img = new Image();
    img.crossOrigin = 'Anonymous'; // 支持跨域图片

    img.onload = () => {
      // 检查组件是否已卸载
      if (isUnmounted.value) {
        return;
      }

      try {
        // 使用安全的setTimeout让loading动画有时间显示
        createSafeTimeout(() => {
          try {
            // 再次检查组件是否已卸载和DOM是否存在
            if (isUnmounted.value || !canvasWrapperRef.value) {
              return;
            }

            image.value = img;

            const canvas = canvasWrapperRef.value;
            const canvasWidth = canvas.clientWidth;
            const canvasHeight = canvas.clientHeight;
            const hScale = canvasWidth / img.naturalWidth;
            const vScale = canvasHeight / img.naturalHeight;

            view.scale = Math.min(hScale, vScale, 1) * 0.9;
            initialScale.value = view.scale; // 缓存初始缩放比例
            view.x = (canvasWidth - img.naturalWidth * view.scale) / 2;
            view.y = (canvasHeight - img.naturalHeight * view.scale) / 2;

            // 初始化比例尺，放置在图片右上角
            const initialRulerLength = props.defaultRulerLength / view.scale; // 使用配置的固定长度
            const rightPadding = 50; // 距离右边缘的距离
            const topPadding = 50;   // 距离上边缘的距离

            // 右上角位置：从右边开始向左放置比例尺
            const rulerEndX = img.naturalWidth - rightPadding;
            const rulerStartX = rulerEndX - initialRulerLength;
            const rulerYPosition = topPadding;

            ruler.p1 = { x: rulerStartX, y: rulerYPosition };
            ruler.p2 = { x: rulerEndX, y: rulerYPosition };
            ruler.visible = true;

            // 使用安全的setTimeout设置提示隐藏
            tooltipTimer = setTimeout(() => {
              if (!isUnmounted.value) {
                showTooltip.value = false;
              }
            }, 5000);

            draw(); // 首次绘制

            loading.value = false;
            imageProcessing.value = false; // 图片处理完成
          } catch (err) {
            if (!isUnmounted.value) {
              loading.value = false;
              imageProcessing.value = false;
              error.value = `图片处理失败: ${err.message}`;
              console.error('图片处理失败:', err);
            }
          }
        }, 100); // 给loading动画100ms的显示时间
      } catch (err) {
        if (!isUnmounted.value) {
          loading.value = false;
          imageProcessing.value = false;
          error.value = `图片处理失败: ${err.message}`;
          console.error('图片处理失败:', err);
        }
      }
    };

    img.onerror = (e) => {
      if (!isUnmounted.value) {
        loading.value = false;
        imageProcessing.value = false;
        error.value = '图片加载失败，请检查图片链接是否有效或网络连接是否正常';
        console.error('图片加载失败:', url, e);
      }
    };

    img.src = url;
  };

  function handleUploadChange({ files, status }) {
    error.value = ''; // 清除之前的错误

    if (!files || files.length === 0) {
      return;
    }

    const file = files[0];

    // 验证文件格式
    if (file.raw && !supportedFormats.includes(file.raw.type)) {
      error.value = `不支持的文件格式。请上传 ${supportedFormats.map(f => f.split('/')[1]).join(', ')} 格式的图片`;
      return;
    }

    // 验证文件大小 (限制为10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.raw && file.raw.size > maxSize) {
      error.value = '文件大小不能超过10MB';
      return;
    }

    // 仅当上传成功时才处理
    if (status && file.response && file.response.url) {
      imageUrl.value = file.response.url;
      loadImage(imageUrl.value);
    } else if (status === 'error') {
      error.value = '文件上传失败，请重试';
    }
  }

  onMounted(() => {
    if (canvasRef.value) {
      ctx.value = canvasRef.value.getContext('2d');
    }
  });

  watch(view, draw, { deep: true });

  // 监听外部传入的 imageUrl prop 变化（仅在 showUpload 为 false 时生效）
  watch(
    () => props.imageUrl,
    (newImageUrl, oldImageUrl) => {
      // 只有在不显示上传组件时才监听外部图片URL变化
      if (!props.showUpload && newImageUrl && newImageUrl.trim() !== '') {
        // 避免重复加载相同的图片
        if (newImageUrl !== oldImageUrl && newImageUrl !== imageUrl.value) {
          // 更新内部图片URL状态
          imageUrl.value = newImageUrl;
          // 加载外部传入的图片
          loadImage(newImageUrl);
        }
      }
    },
    { immediate: true } // 立即执行一次，处理初始值
  );

  // 输入框事件处理函数
  const handleInputFocus = () => {
    inputState.isFocused = true;
  };

  const handleInputBlur = () => {
    inputState.isFocused = false;
  };

  const handleInputChange = () => {
    const value = ruler.value;
    // 检查是否超出最大值
    inputState.isOverMax = value != null && !isNaN(value) && value > props.maxValue;
  };

  // 组件销毁时清理资源
  onUnmounted(() => {
    // 设置卸载标志
    isUnmounted.value = true;

    // 清理所有安全延时函数
    cleanupFunctions.forEach(cleanup => cleanup());
    cleanupFunctions.clear();

    // 清理平移相关的事件监听器
    if (panMoveHandler) {
      document.removeEventListener('mousemove', panMoveHandler);
      panMoveHandler = null;
    }
    if (panEndHandler) {
      document.removeEventListener('mouseup', panEndHandler);
      panEndHandler = null;
    }

    // 清理比例尺相关的事件监听器
    if (rulerMoveHandler) {
      document.removeEventListener('mousemove', rulerMoveHandler);
      rulerMoveHandler = null;
    }
    if (rulerUpHandler) {
      document.removeEventListener('mouseup', rulerUpHandler);
      rulerUpHandler = null;
    }

    // 清理定时器
    if (tooltipTimer) {
      clearTimeout(tooltipTimer);
      tooltipTimer = null;
    }

    // 清理其他资源
    if (ctx.value) {
      ctx.value = null;
    }
    if (image.value) {
      image.value = null;
    }
  });

  // --- 交互逻辑 ---

  let isPannable = false;
  let lastPanX = 0;
  let lastPanY = 0;

  // 事件监听器引用，用于清理
  let panMoveHandler = null;
  let panEndHandler = null;
  let rulerMoveHandler = null;
  let rulerUpHandler = null;

  const handlePanStart = (e) => {
    if (e.target.closest('.ruler-svg')) return;
    isPannable = true;
    lastPanX = e.clientX;
    lastPanY = e.clientY;
    if (canvasWrapperRef.value) canvasWrapperRef.value.style.cursor = 'grabbing';

    // 保存事件监听器引用
    panMoveHandler = handlePanMove;
    panEndHandler = handlePanEnd;
    document.addEventListener('mousemove', panMoveHandler);
    document.addEventListener('mouseup', panEndHandler);
  };

  const handlePanMove = (e) => {
    if (!isPannable) return;
    const dx = e.clientX - lastPanX;
    const dy = e.clientY - lastPanY;
    view.x += dx;
    view.y += dy;
    lastPanX = e.clientX;
    lastPanY = e.clientY;
  };

  const handlePanEnd = () => {
    if (!isPannable) return;
    isPannable = false;
    if (canvasWrapperRef.value) canvasWrapperRef.value.style.cursor = 'grab';

    // 清理事件监听器
    if (panMoveHandler) {
      document.removeEventListener('mousemove', panMoveHandler);
      panMoveHandler = null;
    }
    if (panEndHandler) {
      document.removeEventListener('mouseup', panEndHandler);
      panEndHandler = null;
    }
  };

  const handleWheel = (e) => {
    if (!canvasWrapperRef.value || !image.value) return;
    const rect = canvasWrapperRef.value.getBoundingClientRect();

    // 计算能完整显示图片的最小缩放比例
    const fitScaleX = rect.width / image.value.naturalWidth;
    const fitScaleY = rect.height / image.value.naturalHeight;
    const minScale = Math.min(fitScaleX, fitScaleY);

    const offsetX = e.clientX - rect.left;
    const offsetY = e.clientY - rect.top;

    const oldScale = view.scale;
    const newScale = oldScale * (1 - e.deltaY / 500);
    view.scale = Math.max(minScale, Math.min(newScale, 10)); // 保证缩放比例不小于minScale

    view.x = offsetX - ((offsetX - view.x) * view.scale) / oldScale;
    view.y = offsetY - ((offsetY - view.y) * view.scale) / oldScale;
  };

  const handleRulerMouseDown = (e, type) => {
    ruler.dragging = true;
    ruler.dragType = type;

    // 获取相对于canvas容器的坐标，这样与平移逻辑保持一致
    const rect = canvasWrapperRef.value.getBoundingClientRect();
    ruler.startX = e.clientX - rect.left;
    ruler.startY = e.clientY - rect.top;
    ruler.startP1 = { ...ruler.p1 };
    ruler.startP2 = { ...ruler.p2 };

    // 保存事件监听器引用
    rulerMoveHandler = handleRulerMouseMove;
    rulerUpHandler = handleRulerMouseUp;
    document.addEventListener('mousemove', rulerMoveHandler);
    document.addEventListener('mouseup', rulerUpHandler);
  };

  const handleRulerMouseMove = (e) => {
    if (!ruler.dragging) return;

    // 获取相对于canvas容器的当前坐标
    const rect = canvasWrapperRef.value.getBoundingClientRect();
    const currentX = e.clientX - rect.left;
    const currentY = e.clientY - rect.top;

    // 计算移动距离并转换到图片坐标系
    const dx = (currentX - ruler.startX) / view.scale;
    const dy = (currentY - ruler.startY) / view.scale;

    if (ruler.dragType === 'move') {
      ruler.p1.x = ruler.startP1.x + dx;
      ruler.p1.y = ruler.startP1.y + dy;
      ruler.p2.x = ruler.startP2.x + dx;
      ruler.p2.y = ruler.startP2.y + dy;
    } else if (ruler.dragType === 'p1') {
      ruler.p1.x = ruler.startP1.x + dx;
      ruler.p1.y = ruler.startP1.y + dy;
    } else if (ruler.dragType === 'p2') {
      ruler.p2.x = ruler.startP2.x + dx;
      ruler.p2.y = ruler.startP2.y + dy;
    }
  };

  const handleRulerMouseUp = () => {
    // 当用户结束拖动P1或P2手柄时，进行角度吸附检查
    if (ruler.dragType === 'p1' || ruler.dragType === 'p2') {
      const angle = rulerAngle.value; // 角度值范围: -180 到 180
      const threshold = props.snapAngleThreshold;
      let targetAngleRad = null;

      // 检查是否接近水平方向 (0° or 180°)
      if (Math.abs(angle) <= threshold) {
        targetAngleRad = 0;
      } else if (Math.abs(Math.abs(angle) - 180) <= threshold) {
        targetAngleRad = Math.PI;
      }
      // 检查是否接近垂直方向 (90° or -90°)
      else if (Math.abs(Math.abs(angle) - 90) <= threshold) {
        targetAngleRad = angle > 0 ? Math.PI / 2 : -Math.PI / 2;
      }

      // 如果满足吸附条件，则重新计算端点
      if (targetAngleRad !== null) {
        ruler.snapping = true;

        const length = rulerLength.value;
        const centerX = (ruler.p1.x + ruler.p2.x) / 2;
        const centerY = (ruler.p1.y + ruler.p2.y) / 2;
        const halfLength = length / 2;

        const cos = Math.cos(targetAngleRad);
        const sin = Math.sin(targetAngleRad);

        const newP1 = {
          x: centerX - halfLength * cos,
          y: centerY - halfLength * sin
        };
        const newP2 = {
          x: centerX + halfLength * cos,
          y: centerY + halfLength * sin
        };

        // 使用动画过渡到新位置
        animateRulerToPosition(newP1, newP2);
      }
    }

    ruler.dragging = false;

    // 清理事件监听器
    if (rulerMoveHandler) {
      document.removeEventListener('mousemove', rulerMoveHandler);
      rulerMoveHandler = null;
    }
    if (rulerUpHandler) {
      document.removeEventListener('mouseup', rulerUpHandler);
      rulerUpHandler = null;
    }
  };

  // 动画函数：平滑过渡到目标位置
  const animateRulerToPosition = (targetP1, targetP2) => {
    const startP1 = { ...ruler.p1 };
    const startP2 = { ...ruler.p2 };
    const duration = 200; // 动画持续时间(ms)
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用缓动函数 (ease-out)
      const easeOut = 1 - Math.pow(1 - progress, 3);

      ruler.p1.x = startP1.x + (targetP1.x - startP1.x) * easeOut;
      ruler.p1.y = startP1.y + (targetP1.y - startP1.y) * easeOut;
      ruler.p2.x = startP2.x + (targetP2.x - startP2.x) * easeOut;
      ruler.p2.y = startP2.y + (targetP2.y - startP2.y) * easeOut;

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        ruler.snapping = false;
      }
    };

    requestAnimationFrame(animate);
  };

  // 隐藏提示
  const hideTooltip = () => {
    showTooltip.value = false;
    if (tooltipTimer) {
      clearTimeout(tooltipTimer);
      tooltipTimer = null;
    }
  };

  // 预览图片
  const handlePreview = () => {
    previewVisible.value = true;
  };

  // 返回事件
  const handleClose = () => {
    emit('close');
  }

  // 设置预览可见性
  const setPreviewVisible = (visible) => {
    previewVisible.value = visible;
  };

  // 删除图片
  const handleDelete = () => {
    if (uploaderRef.value) {
      uploaderRef.value.handleClear();
    }
    // 清空图片URL
    imageUrl.value = '';

    // 重置比例尺状态
    ruler.visible = false;
    ruler.p1 = { x: 100, y: 100 };
    ruler.p2 = { x: 200, y: 100 };
    ruler.value = 100;
    ruler.dragging = false;
    ruler.dragType = '';
    ruler.snapping = false;

    // 重置视图状态
    view.scale = 1;
    view.x = 0;
    view.y = 0;
    initialScale.value = 1; // 重置初始缩放比例

    // 清理图片对象
    if (image.value) {
      image.value = null;
    }

    // 清理Canvas
    if (ctx.value && canvasRef.value) {
      ctx.value.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
    }

    // 重置错误和加载状态
    error.value = '';
    loading.value = false;
    imageProcessing.value = false;

    // 重置提示状态
    showTooltip.value = true;
    if (tooltipTimer) {
      clearTimeout(tooltipTimer);
      tooltipTimer = null;
    }

    // 通知外部组件图片已删除
    emit('change', {
      pixelRate: 0,
      imageUrl: '',
    });
  };
</script>

<style lang="stylus" scoped>
  .ruler-tool-container
    display flex
    flex-direction column
    gap 16px
    .tip
      font-size 14px
      line-height 22px
      font-weight 400

      color: rgba(0, 0, 0, 0.45)

  .container
    position: relative
    width 100%
    height 454px
    display flex
    justify-content center
    align-items center
    background-color var(--bg-color)
    overflow hidden


  .uploader-overlay
    position: absolute
    top: 0
    bottom: 0
    right: 0
    left: 0
    width: 100%
    height: 100%
    z-index: 10
    display: flex
    justify-content: center
    align-items: center
    :deep(.ant-upload) {
      display flex;
      justify-content: center;
      align-items center;
      flex 1;
      width: 100%;
      height: 100%;
      cursor pointer;
    }
    :deep(.uploader-container) {
      width: 100%
      height: 100%
    }

  .canvas-wrapper
    width 100%
    height 100%
    overflow hidden
    position relative
    cursor grab
    border-radius 8px
    &.active {
      border 1px solid rgba(0, 0, 0, 0.15)
    }


  canvas
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;

  .image-processing-overlay
    position: absolute
    top: 0
    left: 0
    width: 100%
    height: 100%
    background: rgba(255, 255, 255, 0.9)
    display: flex
    flex-direction: column
    align-items: center
    justify-content: center
    z-index: 20
    backdrop-filter: blur(2px)

  .custom-loading-indicator
    .loading-circle
      width: 40px
      height: 40px
      border: 3px solid #f0f0f0
      border-top: 3px solid var(--opn--primary-color)
      border-radius: 50%
      animation: spin 1s linear infinite

  .loading-text
    margin-top: 16px
    color: #666
    font-size: 14px

   .rule-actions
     position: absolute
     top: 16px
     left: 16px
     display: flex
     gap: 8px
     z-index: 15

  .image-actions
    position: absolute
    bottom: 16px
    left: 16px
    display: flex
    gap: 8px
    z-index: 15

  .action-btn
    display: flex;
    width: 48px;
    height: 48px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 3px;
    background: rgba(0, 0, 0, 0.7)
    cursor: pointer
    transition: all 0.2s ease
    border-radius: 6px
    overflow: hidden
    :deep(i) {
      line-height 18px;
      color: rgba(255, 255, 255, 0.80);
    }
    span {
      color: rgba(255, 255, 255, 0.80);
      font-family: "PingFang SC";
      font-size: 9px;
      font-style: normal;
      font-weight: 400;
      line-height: 15px; /* 166.667% */
    }
    &:hover
      i,span {
        color: #FFFFFF
      }

    &:active
      i,span {
        color: #FFFFFF
      }

    svg
      flex-shrink: 0

  .ruler-tooltip
    position: absolute
    top: 10px
    left: 50%
    transform: translateX(-50%)
    z-index: 20
    animation: fadeInDown 0.3s ease-out

    .tooltip-content
      background: rgba(0, 0, 0, 0.8)
      color: white
      padding: 8px 12px
      border-radius: 4px
      font-size: 12px
      white-space: nowrap
      box-shadow: 0 2px 8px rgba(0,0,0,0.2)
      display: flex
      align-items: center
      gap: 8px

      .tooltip-close
        background: none
        border: none
        color: white
        font-size: 16px
        cursor: pointer
        padding: 0
        width: 16px
        height: 16px
        display: flex
        align-items: center
        justify-content: center
        border-radius: 50%
        transition: background-color 0.2s ease

        &:hover
          background-color: rgba(255, 255, 255, 0.2)

  .ruler-container
    position: absolute
    top: 0
    left: 0
    width: 100%
    height: 100%
    pointer-events: none

  .ruler-svg
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: visible; // 允许ruler的input部分超出
    pointer-events: none;

  .ruler-line
    cursor: move
    pointer-events: auto

    &.dragging
      filter: drop-shadow(0 4px 8px rgba(0,0,0,0.25))

  .ruler-handle
    cursor: ew-resize
    pointer-events: auto
    transition: fill 0.2s ease, stroke 0.2s ease

    &:hover
      stroke: #7A4F1A
      fill: #FFF8E7
      filter: drop-shadow(0 2px 4px rgba(0,0,0,0.15))

  .upload-placeholder
    cursor: pointer
    position: relative

  .upload-main
    display: flex
    flex-direction: column
    align-items: center
    color: #606266

  .upload-icon
    font-size: 40px
    line-height: 1
    font-weight: 400
    color: rgba(0, 0, 0, 0.85)
    transition: transform 0.3s ease

    &.loading
      animation: rotate 1s linear infinite
      color: var(--opn--primary-color)

  .upload-text
    margin-top: 12px
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45)

  .upload-hint
    position: absolute
    bottom: 32px
    color: #909399
    font-size: 12px
    text-align: center

  .upload-error
    position: absolute
    bottom: 16px
    color: #f56c6c
    font-size: 12px
    text-align: center
    background: rgba(245, 108, 108, 0.1)
    padding: 4px 8px
    border-radius: 4px
    max-width: 90%

  .ruler-input-wrapper
      display: flex
      align-items: center
      background: rgba(255, 255, 255, 0.95)
      padding: 6px 10px
      border-radius: 6px
      border: 1px solid #D9D9D9
      box-shadow: 0 2px 8px rgba(0,0,0,0.15)
      pointer-events: auto
      backdrop-filter: blur(4px)
      transition: border-color 0.2s ease

      &.focused
        border-color: #CCA054

      &.over-max
        border-color: #FF2121

      input
          width: 60px
          border: none
          outline: none
          text-align: right
          font-size: 14px
          font-weight: 500
          color: #333
          background: transparent
          -moz-appearance: textfield
          &::-webkit-outer-spin-button,
          &::-webkit-inner-spin-button
            -webkit-appearance: none
            margin: 0

      span
          margin-left: 4px
          font-size: 12px
          font-weight: 500
          color: #99641F

  @keyframes rotate
    from
      transform: rotate(0deg)
    to
      transform: rotate(360deg)

  @keyframes spin
    from
      transform: rotate(0deg)
    to
      transform: rotate(360deg)

  @keyframes fadeInDown
    from
      opacity: 0
      transform: translateX(-50%) translateY(-10px)
    to
      opacity: 1
      transform: translateX(-50%) translateY(0)
</style>
