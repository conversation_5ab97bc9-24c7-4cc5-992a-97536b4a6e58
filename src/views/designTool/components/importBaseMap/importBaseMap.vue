<script setup>
  import { useGlobalModalStore, IMPORT_BASE_MAP } from '@/store/modules/globalModalStore';
  import { nextTick, ref, toRaw } from 'vue';
  import { message } from '@syn/ant-design4-vue3';
  import CustomTabs from '@/components/customTabs/customTabs.vue';
  import { useCaseStatusStore } from '@/store/modules/caseStatusStore';
  import { isFunction } from '@/utils/isType';
  import { useUserStore } from '@/store/modules/userStore';
  import { gioTrackReport } from '@/utils/gioTrack';
  import ProductListPage from './productList/ProductListPage.vue';
  import RulerTool from './rulerTool/index.vue';
  import OcrTool from './ocrTool/index.vue';
  import { _API_recogniseHouse } from '@/api/recogniseHouse';
  import { useModal } from '@/hooks/useModal';

  const globalModalStore = useGlobalModalStore();
  const caseStatusStore = useCaseStatusStore();
  const importBaseStore = globalModalStore[IMPORT_BASE_MAP];
  const emit = defineEmits(['onAction']);
  const open = ref(true);

  const userStore = useUserStore();

  const emptyRulerData = {
    pixelRate: 0,
    rulerValue: 100,
    opacity: 1,
    imageUrl: '',
  };

  const emptyOcrData = {
    imageUrl: '',
  };

  const props = defineProps({
    title: {
      type: String,
      default: '导入底图',
    },
    caseId: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: 'importBaseMap',
    },
  });
  const selectType = ref('fromLibrary');
  const confirmLoading = ref(false);
  // 户型库
  const rulerLIData = ref({ ...emptyRulerData });
  // 本地
  const rulerLOData = ref({ ...emptyRulerData });
  // 户型库
  const ocrLIData = ref({ ...emptyOcrData });
  // 本地
  const ocrLOData = ref({ ...emptyOcrData });
  const tabs = ref([
    {
      key: 'fromLibrary',
      title: '从户型库选择',
      gioData: {
        importBaseMap: 'CZHT30621',
        recognizeHouse: 'CZHT30623',
      },
    },
    {
      key: 'fromLocal',
      title: '手动选择',
      gioData: {
        importBaseMap: 'CZHT30622',
        recognizeHouse: 'CZHT30624',
      },
    },
  ]);

  /**
   * @description 埋点上报
   */
  function gioTrack() {
    let selectItem = tabs.value.find((item) => item.key === selectType.value);
    if (selectItem) {
      gioTrackReport(selectItem.gioData[props.type]);
    }
  }
  /**
   * @description 导入底图的确认事件
   */
  function handleOk() {
    if (props.type !== "importBaseMap") {
      handleSaveOcrData();
      return;
    }
    handleSaveRulerData();
  }

  // 保存比例尺信息
  function handleSaveRulerData() {
    const isFromLibrary = selectType.value === "fromLibrary";
    console.log("caseStatusStore", caseStatusStore);
    const payload = toRaw(isFromLibrary ? rulerLIData.value : rulerLOData.value);
    if (!payload.imageUrl) {
      message.warn(isFromLibrary ? '请选择底图' : '请上传底图');
      return;

    }
    if (payload.pixelRate <= 0) {
      message.warn('请完善比例尺信息');
      return;
    }
    console.log("payload.rulerValue", payload.rulerValue);
    if (payload.rulerValue < 100 || payload.rulerValue > 10000) {
      message.warn('比列尺数值范围请控制在100-10000内!');
      return;
    }
    const {
      drawContent,
      hasBackgroundImage,
      hasSpace,
      hasIntelligentGoods,
    } = caseStatusStore.statusMap;
    if (drawContent || hasBackgroundImage || hasSpace || hasIntelligentGoods) {
      useModal({
        type: "confirm",
        title: '提示',
        content: '您确认要导入新底图么, 导入后将清空当前绘制的户型',
        onOk: () => {
          emit('onAction', {
            action: 'clearTool',
          });
          nextTick(() => {
            handleEventRulerData(payload);
          });
        },
      });
      return;
    }
    handleEventRulerData(payload);
  }

  function handleEventRulerData(payload) {
    confirmLoading.value = true;
    gioTrack();
    emit('onAction', {
      action: 'loadBaseMap',
      payload,
    });
    confirmLoading.value = false;
    handleCancel();
    message.success("导入底图成功!");
  }

  // 保存Ocr信息
  function handleSaveOcrData() {
    const isFromLibrary = selectType.value === "fromLibrary";
    const { imageUrl } = toRaw(isFromLibrary ? ocrLIData.value : ocrLOData.value);
    if (!imageUrl) {
      message.warn(isFromLibrary ? '请选择户型图' : '请上传户型图');
      return;
    }
    const {
      drawContent,
      hasBackgroundImage,
      hasSpace,
      hasIntelligentGoods,
    } = caseStatusStore.statusMap;
    if (drawContent || hasBackgroundImage || hasSpace || hasIntelligentGoods) {
      useModal({
        type: "confirm",
        title: '提示',
        content: '识别户型会清空当前绘制的户型，是否继续?',
        onOk: () => {
          emit('onAction', {
            action: 'clearTool',
          });
          nextTick(() => {
            handleEventOcrData(imageUrl);
          });
        },
      });
      return;
    }
    handleEventOcrData(imageUrl);
  }

  function handleEventOcrData(imageUrl) {
    confirmLoading.value = true;
    _API_recogniseHouse(imageUrl)
      .then((res) => {
        if (res.code === '0') {
          gioTrack();
          emit('onAction', {
            action: 'recogniseHouse',
            payload: {
              image: imageUrl,
              data: res.data,
            },
          });
          message.success("户型识别成功，请对比户型图确认是否完整");
          handleCancel();
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  function handleCancel() {
    // 调用对应modal的Event
    if (isFunction(importBaseStore?.event)) {
      importBaseStore.event();
    }
    globalModalStore.clearStoreState(IMPORT_BASE_MAP);
    ocrLIData.value = {
      ...emptyOcrData,
    };
    ocrLOData.value = {
      ...emptyOcrData,
    };
    rulerLIData.value = {
      ...emptyRulerData
    };
    rulerLOData.value = {
      ...emptyRulerData
    };
  }

  function handleRulerChange(res, address, street) {
    if (selectType.value === 'fromLibrary') {
      rulerLIData.value = {
        opacity: 0.5,
        ...res,
      };
    } else {
      rulerLOData.value = {
        opacity: 0.5,
        ...res,
      };
    }
    userStore.setAddressInfo(address || {}, street);
  }

  function handelOcrChange(res, address, street) {
    if (selectType.value === 'fromLibrary') {
      ocrLIData.value = res;
    } else {
      ocrLOData.value = res;
    }
    userStore.setAddressInfo(address || {}, street);
  }

  function handelClear() {
    ocrLIData.value = {
      ...emptyOcrData
    };
    rulerLIData.value = {
      ...emptyRulerData
    };
  }
</script>

<template>
  <a-modal
    v-model:open="open"
    :title="title"
    :destroyOnClose="true"
    :maskClosable="false"
    :keyboard="false"
    :width="824"
    centered
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    wrapClassName="import-base-map-modal"
    @cancel="handleCancel">
    <div class="import-base-map-container">
      <CustomTabs :tabs="tabs" position="center" v-model:activeKey="selectType">
        <template #fromLibrary>
          <ProductListPage :type="type" @change="handleRulerChange" @ocr="handelOcrChange" @clear="handelClear" />
        </template>
        <template #fromLocal>
          <template v-if="type === 'importBaseMap'">
            <RulerTool @change="handleRulerChange" />
          </template>
          <template v-else>
            <OcrTool @change="handelOcrChange" />
          </template>
        </template>
      </CustomTabs>
    </div>
  </a-modal>
</template>

<style scoped lang="stylus">
  .import-base-map-container
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;

  .address-selector-container
    margin-bottom: 20px
    display flex
    flex-direction row
    align-items center
    justify-content flex-start

  .content-area
    position: relative;

  .layout-grid
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 16px;
    height: 300px;
    overflow-y: auto;
    padding: 5px;

    .layout-item
      height: 160px;
      background-color: #f0f2f5;
      border-radius: 4px;
      border 1px solid transparent;
      cursor: pointer;
      transition: border-color 0.3s;
      .info
        padding 0 5px
        @extends .ellipsis
        text-align center

      &.selected
        border-color: #1890ff;
</style>
<style lang="stylus">
  // 全局样式，用于控制 modal 高度
  .import-base-map-modal
    .ant-modal-content
      display flex
      flex-direction column
      height 680px !important
    .ant-modal-body
      display flex
      flex-direction column
      flex 1
</style>
