<template>
  <div class="ocr-tool-container">
    <div class="container">
      <div v-show="!imageUrl" class="uploader-overlay">
        <Uploader
          ref="uploaderRef"
          :max="1"
          :multiple="false"
          width="100%"
          height="454px"
          @onChange="handleUploadChange"
          @dragError="handleDragError"
        />
      </div>
      <div class="ocr-wrapper" :class="{ 'active': !!imageUrl }">
        <client-img
          :src="imageUrl"
          :preview="false"
          :lazy="false"
          :ignoreCompress="true"
          fit="contain"
          class="ocr-wrapper-img"
        />
        <!-- 图片操作按钮 -->
        <div v-if="imageUrl" class="image-actions">
          <div class="action-btn preview-btn" @click="handlePreview" title="预览图片">
            <Icon icon="synEyeOpen" color="#ffffff" :size="18" pointer></Icon>
            <span>预览</span>
          </div>
          <div v-if="showUpload" class="action-btn delete-btn" @click="handleDelete" title="删除图片">
            <Icon icon="synDelete" color="#ffffff" :size="18" pointer></Icon>
            <span>删除</span>
          </div>
          <div v-if="!showUpload && imageUrl" class="action-btn delete-btn" @click="handleClose" title="返回">
            <Icon icon="synRefresh" color="#ffffff" :size="18" pointer></Icon>
            <span>重新选择</span>
          </div>
        </div>
      </div>

      <!-- 图片预览 -->
      <a-image
        v-if="imageUrl"
        :src="imageUrl"
        style="display: none;"
        ref="previewImageRef"
        :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
      />
    </div>
    <div v-if="props.showUpload" class="tip">只支持.png .jpg .webp格式</div>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import Icon from '@/components/icon/icon.vue';
  import Uploader from '@/components/upload/uploader.vue';
  import ClientImg from '@/components/clientImg/clientImg.vue';
  import { message } from '@syn/ant-design4-vue3';

  const emit = defineEmits(['change', 'close']);

  const props = defineProps({
    showUpload: {
      type: Boolean,
      default: true,
    },
    imageUrl: {
      type: String,
      default: '',
    },
  });

  const imageUrl = ref('');
  const uploaderRef = ref(null);
  const previewImageRef = ref(null); // 预览图片组件引用

  // 错误状态管理
  const error = ref('');
  const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  // 预览状态管理
  const previewVisible = ref(false);

  function handleDragError() {
    message.warn("仅支持jpg/png/webp格式的文件!");
  }

  function handleUploadChange({ files, status }) {
    error.value = ''; // 清除之前的错误

    if (!files || files.length === 0) {
      return;
    }

    const file = files[0];

    // 验证文件格式
    if (file.raw && !supportedFormats.includes(file.raw.type)) {
      error.value = `不支持的文件格式。请上传 ${supportedFormats.map(f => f.split('/')[1]).join(', ')} 格式的图片`;
      return;
    }

    // 验证文件大小 (限制为10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.raw && file.raw.size > maxSize) {
      error.value = '文件大小不能超过10MB';
      return;
    }

    // 仅当上传成功时才处理
    if (status && file.response && file.response.url) {
      imageUrl.value = file.response.url;
      emit('change', {
        imageUrl: imageUrl.value,
      });
    } else if (status === 'error') {
      error.value = '文件上传失败，请重试';
    }
  }

  // 监听外部传入的 imageUrl prop 变化（仅在 showUpload 为 false 时生效）
  watch(
    () => props.imageUrl,
    (newImageUrl, oldImageUrl) => {
      // 只有在不显示上传组件时才监听外部图片URL变化
      if (!props.showUpload && newImageUrl && newImageUrl.trim() !== '') {
        // 避免重复加载相同的图片
        if (newImageUrl !== oldImageUrl && newImageUrl !== imageUrl.value) {
          // 更新内部图片URL状态
          imageUrl.value = newImageUrl;
          emit('change', {
            imageUrl: imageUrl.value,
          });
        }
      }
    },
    { immediate: true } // 立即执行一次，处理初始值
  );





  // 预览图片
  const handlePreview = () => {
    previewVisible.value = true;
  };

  // 返回事件
  const handleClose = () => {
    emit('close');
  }

  // 设置预览可见性
  const setPreviewVisible = (visible) => {
    previewVisible.value = visible;
  };

  // 删除图片
  const handleDelete = () => {
    if (uploaderRef.value) {
      uploaderRef.value.handleClear();
    }
    // 清空图片URL
    imageUrl.value = '';

    // 重置错误状态
    error.value = '';

    // 通知外部组件图片已删除
    emit('change', {
      imageUrl: '',
    });
  };
</script>

<style lang="stylus" scoped>
  .ocr-tool-container
    display flex
    flex-direction column
    gap 16px
    .tip
      font-size 14px
      line-height 22px
      font-weight 400

      color: rgba(0, 0, 0, 0.45)

  .container
    position: relative
    width 100%
    height 454px
    display flex
    justify-content center
    align-items center
    background-color var(--bg-color)
    overflow hidden


  .uploader-overlay
    position: absolute
    top: 0
    bottom: 0
    right: 0
    left: 0
    width: 100%
    height: 100%
    z-index: 10
    display: flex
    justify-content: center
    align-items: center
    background-color: #FAFAFA
    :deep(.ant-upload) {
      display flex;
      justify-content: center;
      align-items center;
      flex 1;
      width: 100%;
      height: 100%;
      cursor pointer;
    }
    :deep(.uploader-container) {
      width: 100%
      height: 100%
    }

  .ocr-wrapper
    width 100%
    height 100%
    overflow hidden
    position relative
    cursor grab
    border-radius 8px
    &.active {
      border 1px solid rgba(0, 0, 0, 0.15)
    }
    &-img {
      width 100%;
      height 100%;
      object-fit contain;
    }

  .image-actions
    position: absolute
    bottom: 16px
    left: 16px
    display: flex
    gap: 8px
    z-index: 15

  .action-btn
    display: flex;
    width: 48px;
    height: 48px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 3px;
    background: rgba(0, 0, 0, 0.7)
    cursor: pointer
    transition: all 0.2s ease
    border-radius: 6px
    overflow: hidden
    :deep(i) {
      line-height 18px;
      color: rgba(255, 255, 255, 0.80);
    }
    span {
      color: rgba(255, 255, 255, 0.80);
      font-family: "PingFang SC";
      font-size: 9px;
      font-style: normal;
      font-weight: 400;
      line-height: 15px; /* 166.667% */
    }
    &:hover
      i,span {
        color: #FFFFFF
      }

    &:active
      i,span {
        color: #FFFFFF
      }

    svg
      flex-shrink: 0
</style>
