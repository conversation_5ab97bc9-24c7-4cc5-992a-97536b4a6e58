import { uploadFile } from '@/api/comm.api';
import { aliOssUpload, initAliOss } from '@/utils/alioss';
import { useOssStore } from '@/store/modules/ossStore';
import { getAuthCode } from '@/api/proposalApi';
import { message } from 'ant-design-vue';
import { useUserStore } from '@/store/modules/userStore';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { getHeaders } from '@/utils/http/request.config';
import { ref } from 'vue';
import { v4 as uuidv4 } from 'uuid';
import { useLoading } from '@/hooks/useLoading';
import { useDesignCase } from '@/hooks/useDesignCase';

const { saveData, getPlanStatusFromEngine } = useDesignCase();
const { hideLoading } = useLoading();
import { useModal } from '@/hooks/useModal';
import { _API_getQuotePreview, _API_quoteSave } from '@/api/intelligentApi';
import { getMdInfo } from '@/hooks/useMdInfo';

/**
 * @description 替换oss代理地址
 * @param url
 * @param ossInfo
 * @returns {*}
 */
function replaceOssProxyUrl(url, ossInfo) {
  let ossInstanceInfo = ossInfo;
  let ossProxyUrl = 'https://static-zjsj.haier.net';
  let ossUrl = `https://${ossInstanceInfo.bucketName}.oss-${ossInstanceInfo.regionId}.aliyuncs.com`;
  return url.replace(ossUrl, ossProxyUrl);
}

/**
 * @description 将图片数据转换为URL
 * @param data
 * @param type 接口上传  OSS上传
 * @returns {Promise<unknown>}
 */
export function imgDataToUrl(data, type = 1) {
  const ossStore = useOssStore();
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    if (data) {
      let randomFileName = uuidv4();
      let file = new File([data], `${randomFileName}.jpg`, {
        type: 'image/jpg',
      });
      console.log('file', file);
      if (type === 1) {
        ossStore
          .getOSSInfo()
          .then(async (res) => {
            let ossInstance = await initAliOss(res);
            let fileName = `${res.folder}${randomFileName}.jpg`;
            aliOssUpload(ossInstance, file, fileName, res)
              .then(async (uRes) => {
                let fileUrl = uRes.res.requestUrls[0].split('?')[0];
                let result = replaceOssProxyUrl(fileUrl, res);
                resolve(result);
              })
              .catch((err) => {
                reject(err);
              });
          })
          .catch((err) => {
            reject(err);
          });
      } else {
        let formData = new FormData();
        formData.append('file', file);
        let uploadRes = await uploadFile(formData);
        if (uploadRes.status === '0') {
          resolve(uploadRes?.data?.url);
        } else {
          reject(new Error('上传失败'));
        }
      }
    } else {
      reject(new Error('No image data provided'));
    }
  });
}

/**
 * @description 保存方案
 * @param instance
 * @param payload
 */
export async function handleSaveCase(
  instance,
  { width = 1920, height = 1080, systemId = '', showAnnotations = false }
) {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      let res = await instance.capturePreview(systemId, width, height, showAnnotations);
      console.log('渲染图', systemId, width, height, showAnnotations, res);
      let img = await imgDataToUrl(res?.data?.data);
      resolve(img);
    } catch (error) {
      console.error('Error capturing preview:', error);
      hideLoading();
      reject(error);
    }
  });
}

/**
 * @description 生成电子提案书图片
 * @param instance
 * @param width
 * @param height
 * @param systemId
 * @param showAnnotations
 * @param isElectricPlan
 * @returns {Promise<unknown>}
 */
export async function handleCreateProposalImg(
  instance,
  { width = 800, height = 800, systemId = 'ALL', showAnnotations = false, isElectricPlan = true }
) {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      let res = await instance.capturePreview(systemId, width, height, showAnnotations, isElectricPlan);
      if (res.code !== 0) {
        reject();
        message.error('电子提案渲染图获取失败');
      }
      let imgData = res?.data?.data;
      let leftTop = res?.data?.leftTop;
      let pixelRate = res?.data?.pixelRate;
      let img = await imgDataToUrl(imgData);
      resolve({
        url: img,
        leftTop,
        pixelRate,
      });
    } catch (error) {
      hideLoading();
      console.error('Error capturing preview:', error);
      reject(error);
    }
  });
}

/**
 * @description 打开电子提案生产界面
 * @param SCHEME_CODE 方案编码
 */
export async function handleOpenProposal(SCHEME_CODE) {
  await getAuthCode()
    .then((res) => {
      if (!res?.data?.authCode) {
        message.error('获取authCode失败，请稍后再试');
        return;
      }
      let mdInfo = getMdInfo();
      let redirectUrl = `${process.env.VUE_APP_SMART_CONTROL_PAGE}?type=smart_control&scheme_code={SCHEME_CODE}&auth_code={AUTH_CODE}&md_code={MD_CODE}`;
      let platformInfo = {
        SCHEME_CODE, // 方案编码
        AUTH_CODE: res?.data?.authCode,
        MD_CODE: mdInfo?.mdCode,
      };
      const result = redirectUrl.replace(/\{(\w+)\}/g, (match, key) => {
        return platformInfo[key] || '';
      });
      window.open(result);
    })
    .catch((error) => {
      message.error('获取authCode失败，请稍后再试');

      console.error('掌上导购获取authCode失败', error);
    });
}

/**
 * @description 获取标注编辑的属性
 * @param info
 * @returns {{}}
 */
export function getAnnotationAttrs(info) {
  return {
    uuid: info.uuid,
    attributeList: [
      {
        title: '标注属性',
        list: [
          {
            label: '字号',
            value: info?.annotation?.fontSize,
            disabled: false,
            key: 'fontSize',
          },
          {
            label: '字体',
            value: info?.annotation?.font,
            disabled: false,
            key: 'font',
          },
          {
            label: '颜色',
            value: info?.annotation?.color,
            disabled: false,
            key: 'color',
          },
          {
            label: '文本',
            value: info?.annotation?.text,
            disabled: false,
            key: 'text',
          },
        ],
      },
    ],
  };
}

// SSE重连相关状态
let sseController = null;
const reconnectAttempts = ref(0);
const maxReconnectAttempts = 5;
const reconnectDelay = ref(1000); // 初始重连延迟1秒
const maxReconnectDelay = 10000; // 最大重连延迟10秒
const isSSEConnected = ref(false);
let reconnectTimer = null;
// 存储engine实例的引用
let engineInstance = null;

/**
 * @description 处理SSE消息
 */
async function handleSSEMessage(e) {
  try {
    const eventType = e.event;
    if (eventType === 'disconnect') {
      await autoSaveCase();
      useModal({
        title: '提示',
        content: e.data || '您的账号已在新的终端登录，当前终端已下线',
        closeable: false,
        hideCancel: true,
        onOk: () => {
          // 设置强制关闭标志，避免beforeunload事件阻止页面关闭
          window.isForceClosing = true;
          window.close();
        },
      });
    }
  } catch (error) {
    console.error('处理SSE消息时出错:', error);
  }
}

/**
 * @description 处理SSE重连
 */
function handleSSEReconnect() {
  if (reconnectAttempts.value >= maxReconnectAttempts) {
    console.error('已达到最大重连次数，停止重连');
    // message.error(`连接失败，已尝试${maxReconnectAttempts}次重连`);
    return;
  }

  reconnectAttempts.value++;

  console.log(`准备第${reconnectAttempts.value}次重连，延迟${reconnectDelay.value}ms`);
  // message.warning(`连接断开，正在尝试第${reconnectAttempts.value}次重连...`);

  // 清除之前的重连定时器
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
  }

  reconnectTimer = setTimeout(() => {
    createSSEConnection();
    // 指数退避算法：每次重连延迟时间翻倍，但不超过最大延迟
    reconnectDelay.value = Math.min(reconnectDelay.value * 2, maxReconnectDelay);
  }, reconnectDelay.value);
}

/**
 * @description 创建SSE连接
 */
export function createSSEConnection() {
  if (sseController) {
    sseController.abort();
  }

  sseController = new AbortController();
  const requestUrl = `${process.env.VUE_APP_URL}/api/server/zjsj/v3/sse/connect`;
  fetchEventSource(requestUrl, {
    method: 'GET',
    headers: {
      ...getHeaders(),
      Accept: 'text/event-stream',
      connection: 'keep-alive',
    },
    signal: sseController.signal,
    openWhenHidden: true,
    onopen(res) {
      console.log('SSE连接已建立', res);
      isSSEConnected.value = true;
      reconnectAttempts.value = 0;
      reconnectDelay.value = 1000;
    },
    onmessage(e) {
      handleSSEMessage(e).then();
    },
    onerror(err) {
      console.error('SSE连接错误:', err);
      isSSEConnected.value = false;

      // 如果不是手动关闭，则尝试重连
      if (!sseController.signal.aborted) {
        handleSSEReconnect();
      }

      // 抛出错误以停止当前连接
      throw err;
    },
  }).catch((err) => {
    if (!sseController.signal.aborted) {
      console.error('SSE连接失败:', err);
      isSSEConnected.value = false;
      handleSSEReconnect();
    }
  });
}

// 处理自动保存方案
async function autoSaveCase() {
  console.log('sse 处理保存方案，engineInstance:', engineInstance);
  if (engineInstance) {
    console.warn('sse 正在保存方案，engineInstance');
    const userStore = useUserStore();
    const isSave = userStore.saveCaseData && 'code' in userStore.saveCaseData;
    const { isPlanChanged } = await getPlanStatusFromEngine(engineInstance);
    if (isSave && isPlanChanged) {
      await saveData(engineInstance, {
        code: userStore.saveCaseData?.code,
        saveType: '2',
      });
    }
  }
}

/**
 * @description 关闭SSE连接
 */
export function closeSSEConnection() {
  if (sseController) {
    sseController.abort();
    sseController = null;
  }

  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }

  isSSEConnected.value = false;
  reconnectAttempts.value = 0;
  reconnectDelay.value = 1000;
}

/**
 * @description 获取SSE连接状态
 */
export function getSSEConnectionStatus() {
  return {
    isConnected: isSSEConnected,
    reconnectAttempts,
    reconnectDelay,
  };
}

// 提供设置engine实例的函数
export function setEngineInstance(engine) {
  engineInstance = engine;
}

/**
 * @description 自动创建报价
 * @param {String} caseCode
 * @param mdInfo
 * @returns
 */
export async function autoCreateQuote(caseCode, mdInfo) {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    let res = await _API_getQuotePreview(caseCode);
    if (res.code === '0') {
      let sRes = await _API_quoteSave({
        caseCode: caseCode,
        ...mdInfo,
      });
      if (sRes.code === '0') {
        resolve(sRes);
      } else {
        reject(sRes);
      }
    } else {
      reject(res);
    }
  });
}
