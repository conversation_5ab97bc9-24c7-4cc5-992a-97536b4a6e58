import { createApp } from 'vue';
import App from './App.vue';
import store from '@/store';

import antd from '@/plugins/ant-design';

import router from '@/router/routes';
import './styles/icon.styl';
import './styles/index.styl';
import synIcon from '@syn/syn-icons';
import '@syn/syn-icons/syn-icons.css';

import setGuard from '@/router/guard';
import { isDev, isProd } from '@/utils/isEnv';
import { MAIN_ACCESS_TOKEN_KEY, MAIN_REFRESH_TOKEN_KEY } from '@/config/const';
import { useUserStore } from '@/store/modules/userStore';
import { useDictStore } from '@/store/modules/dictStore';
import { _api_getCode } from '@/api/comm.api';
import { directives } from '@/directive/directive.js';
import { createModal } from '@/hooks/useModal';
import { getMainPlatformInfo } from '@/utils/http/request.config';
import { useLoading } from '@/hooks/useLoading';

async function bootstrap() {
  const app = createApp(App);
  app.use(antd);
  setGuard(router);
  app.use(store);
  app.use(router);
  app.use(synIcon);

  // 注册全局指令
  Object.keys(directives).forEach((key) => {
    app.directive(key, directives[key]);
  });

  // 获取TOKEN信息
  let info = getMainPlatformInfo();
  const userStore = useUserStore();

  userStore.setToken(info[MAIN_ACCESS_TOKEN_KEY] || '');
  userStore.setRefreshToken(info[MAIN_REFRESH_TOKEN_KEY] || '');
  userStore.setCurrentMd(info.accountDTO?.currentMd); // 缓存当前选择的门店信息
  userStore.saveCaseData = {}; // 清空store中的saveCaseData

  const url = new URL(window.location.href);
  const urlParams = new URLSearchParams(url.search);
  const processingIdFromUrl = urlParams.get('processingId');
  const code = urlParams.get('code');
  const id = urlParams.get('id');
  userStore.setProcessingId(processingIdFromUrl || '');
  userStore.setCaseCode(id || '');
  let { showLoading, hideLoading } = useLoading();
  showLoading();
  // 非编辑 非新建设计
  if (!id && !code) {
    createModal('warning', {
      hideCancel: false,
      maskClosable: true,
      closable: true,
      title: '缺少必要参数',
      onOk: () => {
        console.log('清空当前方案', process.env.VUE_APP_PORTAL_URL);
        // 确保门户地址存在，如果不存在则使用默认地址
        const portalUrl = isProd() ? 'https://syndz.haier.net/#' : 'https://syndz-test.haier.net/#';
        window.location.replace(portalUrl);
      },
      onCancel: () => {
        console.log('onCancel');
      },
    });
    return;
  }
  // 获取页面跳转的参数并进行存储
  if (code && !isDev()) {
    await _api_getCode(code).then((res) => {
      console.log('setCode', JSON.parse(res?.data || '{}'));
      userStore.setPageParams(JSON.parse(res?.data || '{}'));
    });
  }
  hideLoading();
  app.mount('#app');
}

bootstrap()
  .then(() => {
    const dictStore = useDictStore();
    dictStore.setSystemList().finally(); // 设置 企业库一级类目
    dictStore.setHouseTypeList().finally();
  })
  .finally(() => {});
