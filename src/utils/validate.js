/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

export function isPhone(phone) {
  return /^1[3456789]\d{9}$/.test(phone);
}

export function isIntOrTwoDecimal(value) {
  const regex = /^[1-9]\d{0,8}(\.\d{0,2})?$/;
  return regex.test(value);
}

/**
 * URL地址
 * @param {*} s
 */
export function isURL(s) {
  return /^http[s]?:\/\/.*/.test(s);
}

// 检查文件类型是否是合法的图片

export async function detectImageFileType(file) {
  const allowedTypes = ['image/png', 'image/jpeg', 'image/webp'];

  const magicNumbers = {
    '89504e47': 'image/png',
    'ffd8ffe0': 'image/jpeg',
    'ffd8ffe1': 'image/jpeg',
    'ffd8ffe2': 'image/jpeg',
    'ffd8ffe3': 'image/jpeg',
    'ffd8ffe8': 'image/jpeg',
    '52494646': 'image/webp', // RIFF....WEBP
  };

  function readFileHeader(file, length = 12) {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = function () {
        const bytes = new Uint8Array(reader.result).subarray(0, length);
        let header = '';
        for (let i = 0; i < bytes.length; i++) {
          header += bytes[i].toString(16).padStart(2, '0');
        }
        resolve(header);
      };
      reader.readAsArrayBuffer(file.slice(0, length));
    });
  }

  const header = await readFileHeader(file);
  let realType = null;

  for (const [magic, type] of Object.entries(magicNumbers)) {
    if (header.startsWith(magic)) {
      realType = type;
      break;
    }
  }

  const isValid = realType && allowedTypes.includes(realType);
  return { realType, isValid, header };
}
