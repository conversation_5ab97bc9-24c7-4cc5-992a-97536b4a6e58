import { useUserStore } from '@/store/modules/userStore';
import { DESIGNER_TYPE_MAP } from '@/config/const';
import { gioTrack } from '@/utils/gioTrack';
export function zkTrack(params) {
  const userStore = useUserStore();
  let loginInfo = userStore?.zjsjUserInfo;
  let loginUser = loginInfo?.user || {};
  let mdCode = '';
  if (loginUser?.retailStore) {
    let storeCode = JSON.parse(loginUser?.retailStore)?.map((item) => item.code);
    mdCode = (storeCode || []).join(',');
  }
  let gioParams = {
    entrance: params.entrance,
    user_id: loginUser.id, // 设计师平台用户id
    role_type: DESIGNER_TYPE_MAP[loginUser.type],
    industry: loginInfo?.industryTypeValue,
    md_code: mdCode,
    custom_code: loginUser?.shopCode,
  };
  gioTrack('CZHT30616', gioParams);
}
