import { gioData } from './gioData.js';
import { useUserStore } from '@/store/modules/userStore';
import { isProd } from '@/utils/isEnv';
import { isArray } from '@/utils/isType';
const userIdKey = isProd() ? 'user_ID' : 'userId';

const getGioTrackData = () => {
  const userStore = useUserStore();
  console.log('userStore.userInfo?.userId', userStore.userInfo);

  const COM_GIO_TEMPLATE = {
    [userIdKey]: userStore.userInfo?.userId || '',
    role_type: getRoleTypes(),
    md_code: userStore.currentMd?.mdCode || '',
  };

  const GIO_DATA = gioData;
  for (const i in GIO_DATA) {
    if (Object.hasOwn(GIO_DATA[i], 'd')) {
      GIO_DATA[i].d = Object.assign(GIO_DATA[i].d, COM_GIO_TEMPLATE);
    }
  }
  return GIO_DATA;
};

export function gioTrackReport(id, payload = {}) {
  const data = getGioTrackData();
  let gioItem = data[id];
  if (!gioItem) {
    return;
  }
  Object.assign(data[id].d, payload);
  console.log('GIO_TRACK', id, data[id].d);
  window.gio('track', id, data[id].d);
}

/**
 * @description 获取用户类型
 * @returns {string}
 */
function getRoleTypes() {
  const userStore = useUserStore();

  let permissionInfo = isArray(userStore.permissionInfo.roleDetails) ? userStore.permissionInfo.roleDetails : [];
  return permissionInfo.map((item) => item.roleName).join(',');
}
const GIO_TRACK = {
  gioTrackReport,
};
export default GIO_TRACK;
