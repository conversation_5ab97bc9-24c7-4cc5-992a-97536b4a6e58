import { message, Modal } from 'ant-design-vue';
import {
  MAIN_ACCESS_TOKEN_KEY,
  MAIN_PLATFORM_REQUEST_URLS,
  MAIN_PLATFORM_STORAGE_KEY,
  MAIN_REFRESH_TOKEN_KEY,
  PLATFORM,
} from '@/config/const';
import { goToLogin } from '@/utils/gotoPage';
import { isDev } from '@/utils/isEnv';
import { devConfig } from '@/config/devConfig';
import { useLoading } from '@/hooks/useLoading';

const { hideLoading } = useLoading();

let confirm401 = false;

/**
 * 处理错误事件
 * @param {object} error
 * @returns
 */
export function handleError(error) {
  switch (error.response.status) {
    case 404:
      message.error(`接口不存在${error.config.url}`);
      break;
    case 401:
      if (confirm401) {
        return Promise.reject();
      }
      Modal.destroyAll();
      setTimeout(() => {
        console.log('token过期',  hideLoading());
        hideLoading();
      }, 1000);
      Modal.confirm({
        title: '提示',
        content: '登录已过期，请重新登录',
        okText: '确定',
        cancelText: '取消',
        onOk() {
          goToLogin();
          confirm401 = false;
        },
        onCancel() {
          confirm401 = false;
          console.log('Cancel');
        },
      });
      confirm401 = true;
      break;
    default:
      const data = error?.response?.data;
      message.error(data.message);
      break;
  }
}

/**
 * 处理返回事件
 * @param {object} response
 * @returns
 */
export function handleResponse(response) {
  const url = response.config.url;
  let codeList = [0, -2, -3, 1, '0', '2'];
  if (
    codeList.includes(response?.data?.code) ||
    codeList.includes(response?.data?.status) ||
    (response.data?.code === 200 && MAIN_PLATFORM_REQUEST_URLS.includes(url))
  ) {
    return response.data;
  } else {
    let ignoreErrorApiList = [
      '/by_t/',
      '/iam/getTokenAndUserInfo',
      '/register/verifyEmailOrNo',
      '/zjsj/haierhome/user/getRetailStore',
      '/erp/order/v1/management/submit',
    ];
    let isIgnoreError = ignoreErrorApiList.some((item) => {
      return url.includes(item);
    });
    if (isIgnoreError || response.config.ignoreError) {
      return response.data;
    }
    const res = response.data;

    message.error(res.msg ? res.msg : res.data ? res.data : '系统繁忙请稍后重试');
    return response.data;
  }
}

/**
 * @description 获取主站的token信息
 * @returns {{gzt_access_token: string, gzt_refresh_access_token: string, username: string, dkey: string, expires_in: number, uocId: string, accountId: number, external_token_type: string, external_access_token: string, external_refresh_token: string, isHasDesignCircle: boolean, accountDTO: {id: string, mobilePhone: string, sex: null, isTourist: boolean, accountNo: string, email: null, accountName: string, nickname: string, avatar: string, belongIndustry: string, accountType: number, effectiveDate: null, expireDate: null, tenantId: string, orgId: string, channel: number, lastLogin: null, status: number, syndzEnabled: boolean, isDeleted: boolean, lockStatus: boolean, isSocial: boolean, createBy: string, createTime: string, updateBy: string, updateTime: string, syncTime: null, isPrimary: boolean, jshAccountNo: null, uocAccountNo: string, designerName: string, memberId: string, customerList: [{customerId: string, customerName: string, customerType: number, activated: boolean}], currentMd: {mdCode: string, mdName: string, isSynFlag: boolean, roleType: string}}}|any}
 */
export function getMainPlatformInfo() {
  if (isDev()) {
    return devConfig;
  }
  let mainPlatformInfo = localStorage.getItem(MAIN_PLATFORM_STORAGE_KEY);
  return JSON.parse(mainPlatformInfo || '{}');
}

/**
 * 获取携带的token
 * @returns {{'X-Access-Token': (*|string), 'X-Refresh-Token': (*|string)}}
 */
export function getHeaderToken() {
  let mainPlatformInfo = getMainPlatformInfo();
  return {
    'X-Access-Token': mainPlatformInfo[MAIN_ACCESS_TOKEN_KEY],
    'X-Refresh-Token': mainPlatformInfo[MAIN_REFRESH_TOKEN_KEY],
    Authorization: mainPlatformInfo[MAIN_ACCESS_TOKEN_KEY],
  };
}

/**
 * 设置请求头
 * @returns
 */
export function getHeaders() {
  let tokenInfo = getHeaderToken();
  return {
    'X-Access-Token': tokenInfo['X-Access-Token'],
    'X-Refresh-Token': tokenInfo['X-Refresh-Token'],
    Authorization: tokenInfo['Authorization'],
    platform: PLATFORM,
  };
}

export function getMainPlatformHeaders() {
  let tokenInfo = getHeaderToken();
  return {
    'X-Access-Token': tokenInfo['X-Access-Token'],
    'X-Refresh-Token': tokenInfo['X-Refresh-Token'],
    'x-app-id': 'DZPT-GZT',
  };
}
