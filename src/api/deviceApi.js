import { http } from '@/utils/http/request';

/**
 * @description 搜索商品 左侧面板数据
 * @returns {Promise<axios.AxiosResponse<any>>}
 * @private
 */
export function _API_searchDeviceTree(data) {
  return http({
    method: 'post',
    url: `/api/server/zjsj/v3/zk/commonent/search`,
    data,
  });
}

/**
 * @description 查询商品下的配件 左侧面板数据
 * @returns {Promise<axios.AxiosResponse<any>>}
 * @private
 */
export function _API_getAccessoryTree(componentCode) {
  return http({
    method: 'get',
    url: `/api/server/zjsj/v3/zk/commonent/part/list?componentCode=${componentCode}`,
  });
}
