import { http } from '@/utils/http/request.js';

/**
 * @description 获取用户信息
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function _API_getUserInfo() {
  return http.get(`/api/user/profile`);
}

export function _API_getUserInfoByToken() {
  return http.get('/open/front/base/v3/user/userInfoByToken');
}

/**
 * @description 获取服务费率
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function _API_getServiceFeeRate(data) {
  return http.get(`/api/server/zjsj/v3/zk/serviceFeeRate`, { data });
}

/**
 * @description 保存服务费率
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function _API_saveServiceFeeRate(params) {
  return http.post(`/api/server/zjsj/v3/zk/serviceFeeRate/save`, null, { params });
}

/**
 * @description 获取产品折扣率
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function _API_getProductDiscountRate(data) {
  return http.get(`/api/server/zjsj/v3/zk/productDiscountRate`, { data });
}

/**
 * @description 保存产品折扣率
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function _API_saveProductDiscountRate(params) {
  return http.post(`/api/server/zjsj/v3/zk/productDiscountRate/save`, null, { params });
}
