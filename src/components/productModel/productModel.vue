<script setup>
  import { _API_getComponentList, _API_getSystemSybModuleAndProduct } from '@/api/sceneApi';
  import GoodsCard from '@/components/goodsCard/goodsCard.vue';
  import ProductSkeleton from '@/components/productSkeleton/productSkeleton.vue';
  import { CANVAS_IMG_COMPRESS_PARAMS } from '@/config/const';
  import { handleOssImageUrl } from '@/utils/tools';
  import { ref, watch } from 'vue';

  const emit = defineEmits(['update:modelValue', 'select-product', 'close']);
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    selectedSystemId: {
      type: String, // 选中的系统
      default: '',
    },
    selectedSubModule: {
      // {  {subModuleId: '', type: 'room'}
      type: Object,
      default: () => {
        return {};
      },
    },
    selectComponentCodes: {
      type: Array, // 选中的组件code
      default: () => {
        return [];
      },
    },
    title: {
      type: String, // 弹窗标题
      default: '',
    },
    showCountIcon: {
      type: Boolean, // 是否显示数量
      default: true,
    },
    showSelectIcon: {
      type: Boolean, // 是否显示多选图标
      default: true,
    },
    showSearch: {
      type: Boolean, // 是否显示搜索框
      default: true,
    },
    showFilter: {
      type: Boolean, // 是否显示搜索条件
      default: true,
    },
    searchMaxLength: {
      type: Number, // 搜索框可输入最大长度
      default: 10,
    },
  });
  const addProductVisible = ref(false);
  const searchValue = ref('');
  const categories = ref([]);
  const subModuleCategories = ref([]);
  // 商品数据
  const productList = ref([]);
  const isSkeleton = ref(true);
  const searchLoading = ref(false); // 搜索加载状态
  const skeletonList = [1, 2, 3, 4, 5, 6];
  const subModuleProductList = ref([]);
  // 选中的商品
  const selectedProduct = ref([]);
  const isSearch = ref(false);
  watch(
    () => props.modelValue,
    async (newValue) => {
      if (newValue) {
        addProductVisible.value = newValue;
        searchValue.value = '';
        isSearch.value = false;
        searchLoading.value = false; // 重置搜索加载状态
        isSkeleton.value = true;
        // 重置选中状态
        selectedProduct.value = [];
        const params = {
          query: '',
          systemId: '',
          baseSubModuleId: props.selectedSubModule?.subModuleId || '',
        };
        if (props.selectedSubModule.type === 'room') {
          // 左侧边栏点击进入
          await getSystemAndSubModule();
        } else {
          // 子模块点击添加商品进入
          await getSearchProductList(params, 'all');
        }
      }
    },
    {
      immediate: true,
    }
  );
  // 封装商品处理逻辑
  function processProductItem(item, selectedProducts) {
    return {
      ...item,
      selected: selectedProducts.some((s) => s.componentCode === item.componentCode),
      quantity: selectedProducts.find((s) => s.componentCode === item.componentCode)?.quantity || 1,
      compressImgUrl: handleOssImageUrl(item.mainImage, CANVAS_IMG_COMPRESS_PARAMS),
    };
  }
  function clearSearch() {
    searchValue.value = '';
    isSearch.value = false;
    searchLoading.value = false; // 重置搜索加载状态
    if (props.selectComponentCodes.length > 0) {
      handleHasFilter();
    } else {
      changeCategory(categories.value[0]);
    }
  }
  async function searchProduct() {
    searchLoading.value = true; // 开始搜索，显示加载状态
    try {
      if (!searchValue.value && props.selectedSubModule.type === 'room') {
        isSearch.value = false;
        let subActive = subModuleCategories.value.find((item) => item.active);
        categories.value.forEach((c) => {
          if (c.active) {
            c.subModules.forEach((s) => {
              if (s.subModuleName === subActive?.subModuleName) {
                productList.value = s.components.map((item) => processProductItem(item, selectedProduct.value));
              }
            });
          }
        });
        searchLoading.value = false; // 搜索完成，隐藏加载状态
        return;
      }
      const params = {
        query: searchValue.value,
        systemId: '',
        baseSubModuleId: props.selectedSubModule.type === 'subModule' ? props.selectedSubModule?.subModuleId : '',
      };
      await getSearchProductList(params, 'search');
      isSearch.value = true;
    } finally {
      searchLoading.value = false; // 无论成功失败，都隐藏加载状态
    }
  }
  async function getSearchProductList(params, type) {
    try {
      // 如果是搜索操作，searchLoading 已经在 searchProduct 中设置
      // 如果是其他操作（如初始加载），则设置 isSkeleton
      if (type !== 'search') {
        isSkeleton.value = true;
      }
      const res = await _API_getComponentList(params);
      isSkeleton.value = false;
      productList.value = res.data.map((item) => processProductItem(item, selectedProduct.value));
      if (type === 'all') {
        subModuleProductList.value = [...productList.value];
      } else if (type === 'search' && props.selectedSubModule.type === 'subModule') {
        const productMap = new Map(productList.value.map((p) => [p.componentCode, p]));
        subModuleProductList.value.forEach((item) => {
          if (productMap.has(item.componentCode)) {
            productMap.get(item.componentCode).quantity = item.quantity;
          }
        });
        productList.value = [...productMap.values()];
      } else {
        if (props.selectComponentCodes.length > 0) {
          productList.value = productList.value.sort((a, b) => {
            const aSelected = selectedProduct.value.some((s) => s.componentCode === a.componentCode);
            const bSelected = selectedProduct.value.some((s) => s.componentCode === b.componentCode);
            if (aSelected && !bSelected) return -1;
            if (!aSelected && bSelected) return 1;
            return 0;
          });
        }
      }
    } catch (error) {
      console.error('获取产品列表失败:', error);
      isSkeleton.value = false;
    }
  }
  async function getSystemAndSubModule() {
    await _API_getSystemSybModuleAndProduct().then((res) => {
      isSkeleton.value = false;
      props.selectComponentCodes.forEach((code) => {
        res.data.forEach((item) => {
          item.subModules.forEach((subModule) => {
            subModule.components.forEach((component) => {
              if (component.mainImage) {
                component.compressImgUrl = handleOssImageUrl(component.mainImage, CANVAS_IMG_COMPRESS_PARAMS);
              }
              if (component.componentCode === code.componentCode) {
                selectedProduct.value = [
                  ...selectedProduct.value,
                  { ...component, quantity: code.quantity, isSelected: true },
                ];
              }
            });
          });
        });
      });
      res.data.forEach((item, index) => {
        item.active = index === 0;
        // 先收集所有子模块的组件，不进行排序
        const allComponents = item.subModules.reduce((acc, sub) => {
          return acc.concat([...sub.components]);
        }, []);
        // 然后对所有组件统一进行排序，与子模块使用相同的排序逻辑
        const sortedAllComponents = [...allComponents].sort((a, b) => {
          const aSelected = props.selectComponentCodes.some((c) => c.componentCode === a.componentCode);
          const bSelected = props.selectComponentCodes.some((c) => c.componentCode === b.componentCode);
          if (aSelected && !bSelected) return -1;
          if (!aSelected && bSelected) return 1;
          return 0;
        });
        item.subModules.unshift({
          components: sortedAllComponents,
          subModuleName: '全部',
          baseSubModuleId: '',
          active: true,
        });
        item.subModules.forEach((i) => {
          i.components.sort((a, b) => {
            const aSelected = props.selectComponentCodes.some((c) => c.componentCode === a.componentCode);
            const bSelected = props.selectComponentCodes.some((c) => c.componentCode === b.componentCode);
            if (aSelected && !bSelected) return -1;
            if (!aSelected && bSelected) return 1;
            return 0;
          });
        });
      });
      categories.value = res.data;
      if (categories.value.length > 0) {
        if (props.selectedSystemId && props.selectedSubModule.subModuleId) {
          handleHasFilter();
        } else {
          changeCategory(categories.value[0]);
        }
      }
    });
  }
  function handleHasFilter() {
    changeCategory(categories.value.find((item) => item.systemId === props.selectedSystemId));
    let subCategory = subModuleCategories.value.find(
      (item) => item.baseSubModuleId === props.selectedSubModule.subModuleId
    );
    subModuleCategories.value.forEach((item) => {
      item.active = item.baseSubModuleId === subCategory.baseSubModuleId;
    });
    if (props.selectComponentCodes?.length > 0) {
      subCategory.components.forEach((item) => {
        props.selectComponentCodes.forEach((code) => {
          if (item.componentCode === code.componentCode) {
            selectedProduct.value.push({ ...item, quantity: code.quantity });
          }
        });
      });
    }
    console.log(selectedProduct.value);
    productList.value = subCategory.components
      .sort((a, b) => {
        const aSelected = props.selectComponentCodes.some((c) => c.componentCode === a.componentCode);
        const bSelected = props.selectComponentCodes.some((c) => c.componentCode === b.componentCode);
        if (aSelected && !bSelected) return -1;
        if (!aSelected && bSelected) return 1;
        return 0;
      })
      .map((item) => processProductItem(item, selectedProduct.value));
  }
  // 切换系统
  function changeCategory(category) {
    categories.value.forEach((item) => {
      item.active = item.systemId === category.systemId;
    });
    subModuleCategories.value = category.subModules.map((item) => ({
      ...item,
      active: item.subModuleName === '全部',
    }));
    if (subModuleCategories.value.length > 0) {
      productList.value = subModuleCategories.value[0].components.map((item) =>
        processProductItem(item, selectedProduct.value)
      );
    }
  }

  // 切换子分类
  function changeSubCategory(subCategory) {
    subModuleCategories.value.forEach((item) => {
      item.active = item.baseSubModuleId === subCategory.baseSubModuleId;
    });
    productList.value = subCategory.components.map((item) => processProductItem(item, selectedProduct.value));
  }

  const handleClick = (accessory) => {
    accessory.isSelected = true;
    if (props.showSelectIcon) {
      if (!selectedProduct.value.some((item) => item.componentCode === accessory.componentCode)) {
        selectedProduct.value.push({ ...accessory, isSelected: true });
      } else {
        selectedProduct.value = selectedProduct.value.map((item) =>
          item.componentCode === accessory.componentCode ? { ...item, isSelected: true } : item
        );
      }
    } else {
      selectedProduct.value = [{ ...accessory }];
    }
  };

  const handleCancelSelect = (accessory) => {
    selectedProduct.value.forEach((item) => {
      if (item.componentCode === accessory.componentCode) {
        item.isSelected = false;
      }
    });
  };

  const isSelectedAccessory = (accessory) => {
    return selectedProduct.value.some((item) => item.componentCode === accessory.componentCode && item.isSelected);
  };

  const handleCountChange = ({ componentCode, count }) => {
    const item = productList.value.find((acc) => acc.componentCode === componentCode);
    if (item) {
      item.count = count;
      item.quantity = count;
      item.isSelected = true;
      if (!selectedProduct.value.some((item) => item.componentCode === componentCode)) {
        selectedProduct.value.push(item);
      } else {
        for (let i of selectedProduct.value) {
          if (i.componentCode === componentCode) {
            i.quantity = count;
            break;
          }
        }
      }
    }
  };

  function handleOk() {
    let select = selectedProduct.value.filter((item) => item.isSelected);
    console.log('select', select);
    if (select) {
      emit('select-product', select);
    }
    addProductVisible.value = false;
    emit('update:modelValue', false);
  }

  function handleUpdateVisible(val) {
    emit('update:modelValue', val);
    emit('close');
    addProductVisible.value = false;
  }
</script>

<template>
  <a-modal
    class="product-model"
    v-if="addProductVisible"
    v-model:open="addProductVisible"
    :centered="true"
    :title="props.title"
    @ok="handleOk"
    @cancel="handleUpdateVisible(false)"
    width="832px"
    height="560px">
    <!-- 搜索框 -->
    <div v-if="!isSkeleton && props.showSearch" class="search-container">
      <a-input-search
        v-model:value="searchValue"
        placeholder="请输入"
        :maxLength="props.searchMaxLength"
        class="search-input"
        enter-button
        allow-clear
        :loading="searchLoading"
        @clear="clearSearch"
        @search="searchProduct"
        @press-enter="searchProduct"></a-input-search>
    </div>
    <!-- 系统 -->
    <div v-if="!isSearch && !isSkeleton && props.showFilter" class="category-container">
      <div
        v-for="category in categories"
        :key="category.systemId"
        class="category-item"
        :class="{ active: category.active }"
        @click="changeCategory(category)">
        {{ category.systemName }}
      </div>
    </div>
    <!-- 子模块 -->
    <div v-if="props.showFilter && !isSearch && !isSkeleton" class="subcategory-container">
      <div
        v-for="(subCategory, index) in subModuleCategories"
        :key="index"
        class="subcategory-item"
        :class="{ active: subCategory.active }"
        @click="changeSubCategory(subCategory)">
        {{ subCategory.subModuleName }}
      </div>
    </div>
    <div v-if="isSkeleton || searchLoading" class="product-skeleton">
      <div
        v-for="(product, index) in skeletonList"
        :key="product + index"
        class="product-item"
        :class="{ marginRight: (index + 1) % 6 !== 0 }">
        <product-skeleton></product-skeleton>
      </div>
    </div>
    <!-- 商品列表 -->
    <div v-else-if="productList.length > 0 && !isSkeleton && !searchLoading" class="product-container">
      <div
        v-for="(product, index) in productList"
        :key="product.componentCode"
        class="product-item"
        :class="{ marginRight: (index + 1) % 6 !== 0 }">
        <goods-card
          :goodsObj="product"
          :showCountIcon="props.showCountIcon"
          :showSelectIcon="props.showSelectIcon"
          :tagText="
            !props.showSelectIcon &&
            props.selectComponentCodes.find((item) => item.componentCode === product.componentCode)
              ? '当前商品'
              : ''
          "
          :isSelect="isSelectedAccessory(product)"
          @on-click="handleClick"
          @on-cancel="handleCancelSelect"
          @count-change="handleCountChange"></goods-card>
      </div>
    </div>
    <div v-else-if="!isSkeleton && !searchLoading" class="product-empty">
      <div class="text-center">
        <img src="@/assets/images/list_empty.png" alt="" />
        <div class="no-product">暂无搜索结果，请重新输入</div>
      </div>
    </div>
    <template #footer>
      <a-button key="back" @click="handleUpdateVisible(false)">取消</a-button>
      <a-button key="submit" type="primary" @click="handleOk">确认</a-button>
    </template>
  </a-modal>
</template>

<style scoped lang="stylus">
  .product-model :global(.ant-modal-title) {
    width 90%;
    overflow hidden;
    text-overflow ellipsis;
    white-space nowrap;
  }
  .product-model :global(.ant-modal-close) {
    top: 22px;
    inset-inline-end: 22px;
  }
  .search-container
    margin-top 8px;
    .search-input
      width 100%
      border-radius 4px

  .category-container
    display flex
    margin-bottom 16px

    .category-item
      padding 12px 0;
      margin-right 32px;
      cursor pointer
      font-size 14px
      color rgba(0, 0, 0, 0.85)
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 500;
      line-height: 22px;
      &.active
        color var(--opn--primary-color)
        border-bottom 2px solid var(--opn--primary-color)

  .subcategory-container
    display flex
    border-radius 4px

    .subcategory-item
      display: flex;
      padding: 2px 12px;
      min-width 72px;
      justify-content: center;
      align-items: center;
      margin-right 8px
      cursor pointer
      font-size 14px
      color #333
      border-radius 4px
      background: var(--card-hover-gb-color);
      &.active
        color var(--opn--primary-color)
        background: var(--orange-1);


  .product-empty {
    height: 400px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    .text-center {
      text-align center;
    }
    .no-product {
      color: rgba(0, 0, 0, 0.45);
      text-align: center;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }

  .quantity-control {
    display: flex;
    align-items: center;
  }

  .quantity-btn {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.3s;
    padding: 0;

    &:hover {
      color: #1890ff;
    }

    &.disabled {
      color: #d9d9d9;
      cursor: not-allowed;

      &:hover {
        color: #d9d9d9;
      }
    }
  }

  .quantity {
    width: 24px;
    text-align: center;
    font-size: 14px;
    color: #333;
  }

  .product-container
    display flex
    flex-wrap wrap
    max-height 400px
    width: 100%;
    overflow-y scroll
    margin-top 16px;
    .product-item
      width 120px
      box-sizing: border-box
      border-radius 4px
      cursor pointer
      margin-bottom 12px

      &.marginRight {
        margin-right 12px;
      }
  .product-skeleton {
    width: 100%;
    text-align center;
    padding 20px;
    display flex;
    gap 12px;
  }
  .product-item
    width 120px
</style>
