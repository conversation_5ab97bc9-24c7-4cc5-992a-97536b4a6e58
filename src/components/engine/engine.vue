<script setup>
  import { nextTick, onMounted, ref } from 'vue';
  import { useDesignCase } from '@/hooks/useDesignCase'; // 示例引入一个hook，实际可根据项目情况替换
  import { handleOpenProposal } from '@/views/designTool/designTools';
  import { useDeviceAttributesStore } from '@/store/modules/deviceAttributes';
  // import { usePopover } from '@/components/popover';
  import { useCaseDataStore } from '@/store/modules/caseDataStore';
  import { useDictStore } from '@/store/modules/dictStore';
  import { HOUSE_TYPE_LIST, SYSTEM_LIST } from '@/config/dictKeys';
  import { createModal } from '@/hooks/useModal';
  import { useCaseStatusStore } from '@/store/modules/caseStatusStore';
  import { isArray } from '@/utils/isType';
  import { message } from 'ant-design-vue';
  const caseStatusStore = useCaseStatusStore();
  const caseDataStore = useCaseDataStore();
  const dictStore = useDictStore();
  const canvasRef = ref(null);
  const houseImageRef = ref(null);
  const Engine = ref(null);
  const systemList = ref([]);
  const spaceList = ref([]);
  const { undoData, redoData, clearData } = useDesignCase();

  defineProps({
    designJson: {
      type: Object,
      required: true,
      default: () => {},
    },
  });
  const emit = defineEmits(['loaded', 'loadError', 'canvasLoaded', 'importSuccess']);
  /**
   * @description 处理方案相关操作
   * @param item
   * @param payload
   */
  async function engineSdkApi(item, payload) {
    const deviceAttributesStore = useDeviceAttributesStore();
    deviceAttributesStore.setSelectType(null);
    console.log('处理方案操作:', item);
    let action = item.action;
    if (!action) {
      return;
    }
    if (action === 'generateProposal') {
      // TODO... 这里需要从工具处获取JSON
      handleOpenProposal();
      return;
    }
    // 绘制标注
    if (action === 'drawAnnotation') {
      Engine.value.switchMode(1, 'Annotation', item.payload);
      return;
    }

    if (action === 'draw') {
      Engine.value.switchMode(1, payload.type, payload.subType);
      return;
    }

    if (action === 'cancelDraw') {
      Engine.value.switchMode(0);
      return;
    }
    // 拖拽设备
    if (action === 'deviceDrag') {
      await engineSdkApi({
        action: 'cancelDraw',
      });
      let deviceInfo = item.payload.dragEntity?.data;
      let payload = item.payload;
      Engine.value.addDevice({
        uuid: '',
        position: [payload?.position?.x, payload?.position?.y],
        ...deviceInfo,
      });
      return;
    }
    // 户型识别
    if (action === 'recogniseHouse') {
      Engine.value.loadFloorplanFromRecognition(item.payload.image, item.payload.data);
      caseDataStore.setOpacity(0.5);
      return;
    }
    // 修改属性  设备 空间 标注
    if (action === 'setAnnotation') {
      // let data = item.payload;
      Engine.value.setAnnotation(item.deviceUuid, {
        ...item.payload,
      });
      return;
    }

    // 修改空间属性
    if (action === 'modifySpace') {
      Engine.value.setElement(item.uuid, {
        ...item.payload,
      });
      return;
    }
    // 设置地图透明度
    if (action === 'setFloorOpacity') {
      Engine.value.setFloorplanImage({
        opacity: item.payload,
      });
      return;
    }
    // 删除底图
    if (action === 'deleteFloorImage') {
      Engine.value.removeFloorplanImage();
      return;
    }
    // 复制设备
    if (action === 'copyDevice') {
      console.log('copyDevice', item);
      Engine.value.copyDevice(item.payload);
      return;
    }

    // 删除设备
    if (action === 'deleteDevice') {
      Engine.value.removeDevice(item.payload);
      deviceAttributesStore.setAttribute({});
      return;
    }

    // 替换设备
    if (action === 'replaceDevice') {
      const { code } = Engine.value.replaceDevice(item?.payload.uuid, { ...item?.payload?.device });
      if (code === 0) {
        message.success('替换商品成功!');
      } else {
        message.warning('替换商品失败!');
      }

      return;
    }

    // 添加配件
    if (action === 'setAccessory') {
      let accessories = isArray(item?.payload?.accessories) ? item?.payload?.accessories : [];
      Engine.value.setAccessory(item?.payload.deviceUuid, accessories);
      return;
    }
    // 导出方案
    if (action === 'exportPlan') {
      // 处理导出方案逻辑
      console.log('导出方案');
      return;
    }
    // 导入方案
    if (action === 'importPlan') {
      await Engine.value.importPlan(item.payload);
      console.log('导入方案', item);
      emit('importSuccess');
      return;
    }

    // 导入底图
    if (action === 'loadBaseMap') {
      if (Engine.value) {
        let setData = {
          opacity: 0.5,
          ...item.payload,
        };
        caseDataStore.setOpacity(setData.opacity);
        Engine.value.setFloorplanImage({ ...setData });
      }
      return;
    }
    // 撤销
    if (action === 'cancel') {
      undoData(Engine.value);
      return;
    }
    // 恢复
    if (action === 'redo') {
      redoData(Engine.value);
      return;
    }
    // 清空
    if (action === 'clear') {
      createModal('confirm', {
        hideCancel: false,
        maskClosable: true,
        closable: true,
        title: '您确认要清空当前方案吗?',
        onOk: () => {
          clearData(Engine.value);
          deviceAttributesStore.setSelectType(null);
          deviceAttributesStore.setAttribute({});
        },
        onCancel: () => {
          console.log('onCancel');
        },
      });

      return;
    }
    // 处理清空底图 不需要弹窗逻辑
    if (action === 'clearTool') {
      Engine.value.clearAll();
      deviceAttributesStore.setSelectType(null);
      deviceAttributesStore.setAttribute({});
      return;
    }
    // 设置视图缩放
    if (action === 'viewScale') {
      console.log('viewScale', item);
      Engine.value.setView2dScale(item.payload);
      return;
    }
    // 获取空间列表
    if (action === 'getSpaces') {
      let spaceList = Engine.value.getSpaces();
      caseDataStore.setSpaceList(spaceList);
      return;
    }
    // 获取某空间下商品列表
    if (action === 'getDevices') {
      console.log('roomId', item.payload?.roomId);
      let devicesList = Engine.value.getDevices();
      caseDataStore.setDevicesList(devicesList);
      return;
    }
    // 添加设备
    if (action === 'addDevice') {
      let deviceInfo = item.payload;
      console.log('deviceInfo', deviceInfo);
      Engine.value.addDevice(deviceInfo);
      return;
    }
    // 获取已选中的场景列表
    if (action === 'getScenes') {
      let sceneList = Engine.value.getScenes();
      caseDataStore.setSelectSceneList(sceneList);
      return;
    }
    // 设置选中的场景列表
    if (action === 'setScenes') {
      Engine.value.setScenes(item.payload?.scenes);
      return;
    }

    // 空间名称 标注及设备是否显示
    if (action === 'changeSpaceStatue') {
      Engine.value.showSpaceName(item.payload);
      return;
    }
    // 控制标注是否显示
    if (action === 'changeAnnotationStatue') {
      Engine.value.showAnnotations(item.payload);
      return;
    }

    // 控制系统设备是否显示
    if (action === 'changeSystemDeviceStatue') {
      Engine.value.showDevicesBySystemIds(item.payload);
      return;
    }
  }

  /**
   * @description 加载设计工具资源
   */
  async function loadEngine() {
    const deviceAttributesStore = useDeviceAttributesStore();
    const module = await import(/* webpackIgnore: true */ process.env.VUE_APP_3D_ENGINE_URL);
    console.log('module...', module);
    if (!module.Engine) {
      console.error('3D引擎模块加载失败');
      emit('loadError');
      return;
    }
    Engine.value = new module.Engine(canvasRef.value, houseImageRef.value, {
      roomTypes: spaceList.value.map((item) => item.value),
      systemIds: systemList.value.map((item) => String(item.value)),
    });
    // 初始化获取方案的状态
    let caseStatus = Engine.value.getPlanStatus();
    if (caseStatus.code === 0) {
      caseStatusStore.setStatusMap(caseStatus.data);
    }
    Engine.value.addEventListener((e) => {
      console.log('事件通知', e);
      let attributeTypes = ['selectDevice', 'selectSpace', 'selectAnnotation'];
      if (attributeTypes.includes(e.eventType)) {
        deviceAttributesStore.setAttribute({
          selectType: selectTypeMap(e.eventType),
          attribute: e?.data || {},
        });
      }
      if (e.eventType === 'selectDevice') {
        deviceAttributesStore.setSelectType(selectTypeMap(e.eventType));
      } else {
        deviceAttributesStore.setSelectType(selectTypeMap(e.eventType));
      }
      if (e.eventType === 'cancelSelect') {
        // 取消选择隐藏属性面板
        deviceAttributesStore.setAttribute({});
      }
      // 画布缩放监听
      if (e.eventType === 'view2dScale') {
        caseDataStore.setViewScale(e.data);
      }

      if (e.eventType === 'planStatusChanged') {
        caseStatusStore.setStatusMap(e.data);
      }

      if (e.eventType === 'canvasLoaded') {
        // 主画布加载完成
        emit('canvasLoaded');
      }
    });
    emit('loaded');
  }

  /**
   * @description 映射type
   * @param type
   * @returns {*}
   */
  function selectTypeMap(type) {
    let map = {
      selectDevice: 'Device',
      selectSpace: 'Space',
      selectAnnotation: 'Annotation',
    };
    return map[type];
  }
  onMounted(async () => {
    systemList.value = await dictStore.getDictStore(SYSTEM_LIST);
    spaceList.value = await dictStore.getDictStore(HOUSE_TYPE_LIST);
    await nextTick(async () => {
      await loadEngine();
      console.log('3D引擎模块:', module);
    });
  });

  /**
   * @description 组织鼠标右键
   * @param e
   */
  function disableRightClick(e) {
    e.preventDefault();
  }

  defineExpose({
    engineSdkApi,
    engine: Engine,
  });
</script>

<template>
  <div class="design-engine-wrap" id="design-canvas-wrap">
    <!--    主画布canvas    -->
    <canvas class="design-engine" id="main-canvas" ref="canvasRef" @contextmenu="disableRightClick"></canvas>
    <!--    户型图实时画布    -->
    <div class="house_type_canvas_wrap">
      <canvas class="house-image-canvas" ref="houseImageRef"></canvas>
    </div>
  </div>
</template>

<style scoped lang="stylus">
  .design-engine-wrap
    width 100%
    height 100%
  canvas.design-engine
    position absolute
    width 100%
    height 100%
    top 0
    left 0
  .house_type_canvas_wrap
    width 240px
    height 240px
    border-radius: 10px
    background #fff
    padding 12px
    position absolute
    top: 50px
    right 0
    box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0px 9px 29px 0px rgba(0, 0, 0, 0.05), 0px 12px 48px 16px rgba(0, 0, 0, 0.03);
  .house-image-canvas
    width 216px
    height 216px
    border-radius: 5px
</style>
