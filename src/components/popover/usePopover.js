import { createApp } from 'vue';
import PopoverComponent from './popover.vue';
import antDesign from '@/plugins/ant-design.js';

/**
 * 函数式调用Popover组件
 * @param {Object} options - 配置选项
 * @param {String} options.placement - 弹出位置，可选值：'top' | 'left' | 'right' | 'bottom' | 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight' | 'leftTop' | 'leftBottom' | 'rightTop' | 'rightBottom'
 * @param {String|Object|Function} options.content - 弹出内容，可以是HTML字符串、Vue组件或渲染函数
 * @param {Object} options.contentProps - 传递给内容组件的props
 * @param {Number} options.x - X坐标位置
 * @param {Number} options.y - Y坐标位置
 * @param {Function} options.onClose - 关闭回调函数
 * @returns {Object} 返回包含close方法的对象
 */
export function usePopover(options = {}) {
  const { placement = 'top', content = '', contentProps = {}, x = 0, y = 0, onClose = null } = options;

  // 创建容器元素
  const container = document.createElement('div');
  document.body.appendChild(container);
  // 关闭方法
  const close = () => {
    try {
      app.unmount();
      if (container.parentNode) {
        container.parentNode.removeChild(container);
      }
    } catch (error) {
      console.warn('Error unmounting popover:', error);
    }
  };

  // 创建Vue应用实例
  const app = createApp(PopoverComponent, {
    visible: true,
    placement,
    content,
    contentProps,
    x,
    y,
    onClose: () => {
      // 执行用户传入的关闭回调
      if (onClose) {
        onClose();
      }
      // 清理组件
      close();
    },
  });

  // 注册ant-design-vue组件
  app.use(antDesign);

  // 挂载组件
  app.mount(container);

  return {
    close,
  };
}

/**
 * 在鼠标事件位置显示Popover
 * @param {MouseEvent} event - 鼠标事件
 * @param {Object} options - 其他配置选项
 * @returns {Object} 返回包含close方法的对象
 */
export function showPopoverAtEvent(event, options = {}) {
  return usePopover({
    ...options,
    x: event.clientX,
    y: event.clientY,
  });
}

/**
 * 在指定元素附近显示Popover
 * @param {String|Element} target - 目标元素或选择器
 * @param {Object} options - 其他配置选项
 * @returns {Object} 返回包含close方法的对象
 */
export function showPopoverAtElement(target, options = {}) {
  const element = typeof target === 'string' ? document.querySelector(target) : target;
  if (!element) {
    console.warn('Target element not found');
    return { close: () => {} };
  }

  const rect = element.getBoundingClientRect();
  const { placement = 'top' } = options;

  let x = rect.left + rect.width / 2;
  let y = rect.top;

  // 根据placement调整位置
  switch (placement) {
    case 'bottom':
    case 'bottomLeft':
    case 'bottomRight':
      y = rect.bottom;
      break;
    case 'left':
    case 'leftTop':
    case 'leftBottom':
      x = rect.left;
      y = rect.top + rect.height / 2;
      break;
    case 'right':
    case 'rightTop':
    case 'rightBottom':
      x = rect.right;
      y = rect.top + rect.height / 2;
      break;
  }

  return usePopover({
    ...options,
    x,
    y,
  });
}

export default usePopover;
