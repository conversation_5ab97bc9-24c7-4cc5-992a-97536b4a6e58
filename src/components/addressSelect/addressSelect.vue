<template>
  <a-cascader
    v-model:value="selectValue"
    :options="options"
    :load-data="loadData"
    :change-on-select="props.changeOnSelect"
    :placeholder="props.placeholder"
    @change="change" />
</template>
<script setup>
  import { onMounted, ref } from 'vue';
  import { _API_getAddressByPid } from '@/api/comm.api';
  import { isArray } from '@/utils/isType';

  const props = defineProps({
    fourCodes: {
      type: Array,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: '请选择地址',
    },
    max: {
      type: Number,
      default: 4,
    },
    changeOnSelect: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(['onChange']);
  const options = ref([]);

  const selectValue = ref([]);

  /**
   * 初始化级联选择器数据
   * @param selectedOptions
   * @param init
   * @returns {Promise<*>}
   */
  async function loadData(selectedOptions, init) {
    const targetOption = selectedOptions.length > 0 ? selectedOptions[selectedOptions.length - 1] : {};
    let len = selectedOptions.length;
    targetOption.loading = true;
    await _API_getAddressByPid(targetOption.region_code).then((res) => {
      if (props.fourCodes.length === 0) {
        res.data = (isArray(res.data) && res.data.length > 0 ? res.data : []).filter(
          (item) => Number(item.levels) === len
        );
      }
      targetOption.loading = false;
      if (isArray(res.data) && res.data.length) {
        if (len >= props.max) {
          targetOption.children = concatCascader(res.data).map((item) => {
            return { ...item, isLeaf: true };
          });
        } else {
          // 这里判断点击获取的下级数据是否存在
          /**
           *1. 存在的情况下 判断下级数据是否是上一级的子节点
           * 2. 不存在的情况下 直接赋值数据
           */
          targetOption.children =
            targetOption.children &&
            targetOption.children.length &&
            targetOption.children.every((item) => item.superior === targetOption.region_code)
              ? targetOption.children
              : concatCascader(res.data);
        }
      } else {
        targetOption.isLeaf = true;
      }
    });
    options.value = [...options.value];
    if (init) {
      return targetOption.children;
    }
  }

  /**
   * 递归拼接级联选择器数据
   * @param address
   * @returns {*}
   */
  function concatCascader(address) {
    if (!isArray(address)) {
      return;
    }
    return address.map((item) => {
      let children = item.children ? concatCascader(item.children) : [];
      return {
        ...item,
        label: item.region_name,
        value: item.region_code.split('_')[0],
        region_code: item.region_code.split('_')[0],
        children: children,
        isLeaf: Number(item.levels) === props.max - 1,
      };
    });
  }

  /**
   * 初始化级联选择器数据
   * @param selectedOptions
   * @returns {Promise<*>}
   */
  function initAddress(selectedOptions) {
    return loadData(selectedOptions, true);
  }

  /**
   * 查找地址信息
   * @param addressArr
   * @param code
   * @returns {*}
   */
  function findAddressInfo(addressArr, code) {
    return addressArr.find((item) => {
      return item.region_code.split('_')[0] === code;
    });
  }

  /**
   * 级联选择器值改变
   * @param value
   * @param selectedOptions
   */
  function change(value, selectedOptions) {
    emit('onChange', { value: value || [], options: selectedOptions || [] });
  }

  onMounted(async () => {
    await _API_getAddressByPid('000000').then((res) => {
      if (res.status === '0' && isArray(res.data)) {
        options.value = concatCascader(res.data);
      }
    });
    if (props.fourCodes) {
      selectValue.value = [...props.fourCodes].slice(0, props.max);
      let selectedOptions = [];
      let nextItem = options.value.find((item) => {
        return item.region_code.split('_')[0] === props.fourCodes[0];
      });
      for (let i = 0; i < props.fourCodes.length; i++) {
        selectedOptions.push(nextItem);
        await initAddress(selectedOptions);
        if (i >= 1) {
          nextItem = findAddressInfo(nextItem.children, props.fourCodes[i]);
        }
      }
    }
  });
</script>
