<script setup>
  import { computed, ref, useAttrs, watch, onMounted, onUnmounted } from 'vue';
  import synLoadingImage from '@/assets/images/syn-loading.png';
  import { canPreviewImage, getFileTypeWithUrl, handleCompressImg } from '@/utils/tools';
  const defaultImg =
    'data:image/png;base64,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';

  // Props定义
  const props = defineProps({
    lazy: {
      type: Boolean,
      default: true, // 默认启用懒加载
    },
  });

  defineOptions({
    inheritAttrs: false,
  });

  const loadStatus = ref(0);
  const attrs = useAttrs();
  const isIntersecting = ref(!props.lazy); // 懒加载状态，如果不启用懒加载则默认为true
  const imageRef = ref(null); // 图片容器引用
  let observer = null; // IntersectionObserver实例

  const config = ref({
    ...attrs,
    placeholder: false,
    fit: 'contain',
  });

  // 监听src变化
  watch(
    () => attrs.src,
    (newSrc) => {
      if (!newSrc) {
        delete config.value.src;
        config.value.src = attrs.fallback || defaultImg;
        isIntersecting.value = false;
      } else {
        // 重置加载状态
        loadStatus.value = 0;
        // 如果不是懒加载模式，直接更新图片src
        if (!props.lazy) {
          config.value.src = isBase64Image(newSrc)
            ? newSrc
            : attrs.ignoreCompress
              ? newSrc
              : handleCompressImg(newSrc);
        } else {
          // 懒加载模式下，如果已经在视口内，更新图片src
          if (isIntersecting.value) {
            config.value.src = isBase64Image(newSrc)
              ? newSrc
              : attrs.ignoreCompress
                ? newSrc
                : handleCompressImg(newSrc);
          }
        }
      }
    },
    {
      immediate: true,
      deep: true,
    }
   );
   function isBase64Image(str) {
    const base64ImageRegex =
      /^data:image\/(png|jpe?g|gif|webp|svg|ico);base64,([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/;
    return base64ImageRegex.test(str);
  }
  const isBase64 = computed(() => {
    return isBase64Image(attrs.src);
  });

  // fit '' | 'fill' | 'contain' | 'cover' | 'none' | 'scale-down'
  const canPreview = computed(() => {
    return isBase64 || canPreviewImage(attrs.src);
  });
  const type = computed(() => {
    return getFileTypeWithUrl(attrs.src);
  });

  function handleLoadError() {
    loadStatus.value = 1;
    config.value.placeholder = true;
    config.value.src = attrs.fallback || defaultImg;
  }

  // 初始化IntersectionObserver（仅在启用懒加载时）
  onMounted(() => {
    if (props.lazy && imageRef.value) {
      observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting && attrs.src) {
              isIntersecting.value = true;
              // 设置图片src
              config.value.src = isBase64Image(attrs.src)
                ? attrs.src
                : attrs.ignoreCompress
                  ? attrs.src
                  : handleCompressImg(attrs.src);
              // 一旦进入视口就停止观察
              observer.unobserve(entry.target);
            }
          });
        },
        {
          rootMargin: '50px', // 提前50px开始加载
          threshold: 0.1, // 10%可见时触发
        }
      );
      observer.observe(imageRef.value);
    } else if (!props.lazy && attrs.src) {
      // 非懒加载模式，直接设置图片src
      config.value.src = isBase64Image(attrs.src)
        ? attrs.src
        : attrs.ignoreCompress
          ? attrs.src
          : handleCompressImg(attrs.src);
    }
  });

  // 清理observer
  onUnmounted(() => {
    if (observer) {
      observer.disconnect();
    }
  });
</script>

<template>
  <div ref="imageRef" :class="['client-image', attrs.fit]">
    <template v-if="attrs.src">
      <template v-if="canPreview">
        <!-- 懒加载模式：只有进入视口才显示图片 -->
        <template v-if="props.lazy">
          <a-image v-if="isIntersecting" v-bind="config" @error="handleLoadError">
            <template #placeholder v-if="loadStatus !== 1">
              <img :src="synLoadingImage" alt="" class="placeholder-image" />
            </template>
          </a-image>
          <!-- 懒加载占位符 -->
          <div v-else class="lazy-placeholder">
            <img :src="synLoadingImage" alt="" class="placeholder-image" />
          </div>
        </template>
        <!-- 非懒加载模式：直接显示图片 -->
        <a-image v-else v-bind="config" @error="handleLoadError">
          <template #placeholder v-if="loadStatus !== 1">
            <img :src="synLoadingImage" alt="" class="placeholder-image" />
          </template>
        </a-image>
      </template>
      <template v-else>
        <div :class="['preview-item-file']">
          <div :class="[`i-preview-${type}`]"></div>
        </div>
      </template>
    </template>
    <template v-else>
      <a-image v-bind="config"></a-image>
    </template>
  </div>
</template>

<style scoped lang="stylus">
  .preview-item-file{
    width 36px
    height 36px
    display flex
    justify-content center
    align-items center
  }
  .client-image {
    width 100%;
    height 100%;
    cursor pointer;
    overflow hidden
    text-align center
    :deep(.ant-image) {
      width 100%;
      height 100%
      .ant-image-img{
        height 100% !important
      }
    }
    &.cover {
      :deep(.ant-image-img) {
        object-fit cover
      }
    }
    &.contain {
      :deep(.ant-image-img) {
        object-fit contain
      }
    }
    &.fill {
      :deep(.ant-image-img) {
        object-fit fill
      }
    }
    &.scale-down {
      :deep(.ant-image-img) {
        object-fit scale-down
      }
    }
    &.none {
      :deep(.ant-image-img) {
        object-fit none
      }
    }
  }
  .placeholder-image {
    width 20px !important
    height 20px !important
    position absolute
    top 0
    left 0
    right 0
    bottom 0
    margin auto
    animation rotate 2s linear infinite
  }

  .lazy-placeholder {
    width 100%
    height 100%
    display flex
    justify-content center
    align-items center
    position relative
    background-color #f5f5f5
  }

  @keyframes rotate {
    from {
      transform rotate(0deg)
    }
    to {
      transform rotate(360deg)
    }
  }
</style>
