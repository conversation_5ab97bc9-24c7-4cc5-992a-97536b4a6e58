import { h } from 'vue';
import { notification, Button } from 'ant-design-vue';
// 版本检测，更新通知
export default {
  install(app) {
    window.document.body.addEventListener('plugin_web_update_notice', () => {
      // 销毁以前的通知实例
      notification.destroy();
      // 开启通知
      notification.warning({
        message: `系统升级`,
        description: '检测到当前系统有更新,请先保存方案,刷新页面再使用！',
        placement: 'bottomRight',
        duration: 0,
        btn: () =>
          h(
            Button,
            {
              type: 'link',
              size: 'small',
              onClick: () => location.reload(),
            },
            { default: () => '刷新' }
          ),
        getContainer: () => app.config.globalProperties.root,
        onClose: () => {
          window.pluginWebUpdateNotice_.dismissUpdate();
        },
      });
    });
  },
};
