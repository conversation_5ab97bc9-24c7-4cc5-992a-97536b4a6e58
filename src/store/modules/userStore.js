import { DEFAULT_AVATAR_URL, LOCAL_STORAGE_PREFIX_KEY } from '@/config/const';
import { storage } from '@/store/useSessionOrLocal';
import { defineStore } from 'pinia';
import { _API_getSignedUrl } from '@/api/dzptApi';

export const useUserStore = defineStore('userStore', {
  state: () => ({
    token: '',
    refresh_token: '',
    userInfo: {},
    zjsjUserInfo: {}, // 设计平台的用户信息
    currentMd: {}, // 定制平台2.0工作台选择的门店
    pageParams: {}, // 页面所需参数
    processingId: '', // 流程id
    saveCaseData: {}, // 保存方案后数据
    addressInfo: {}, // 保存用户户型选择查询地址
    caseCode: '', // 方案编码
    version: '', // 版本
    permissionInfo: {}, // 定制平台2.0的权限信息
  }),
  actions: {
    /**
     * @description 权限信息
     * @param permission
     */
    setPermissionInfo(permission) {
      this.permissionInfo = permission;
    },
    /**
     * @description 缓存当前选择的门店信息
     * @param currentMd
     */
    setCurrentMd(currentMd) {
      this.currentMd = currentMd;
    },
    /**
     * @description 获取设计平台用户信息
     * @param userInfo
     */
    setZjsjUserInfo(userInfo) {
      this.zjsjUserInfo = userInfo;
    },
    /**
     * @description 保存户型库选择的地址信息
     * @param value
     */
    setAddressInfo(value) {
      this.addressInfo = value;
    },
    async getAvatar() {
      if (this.userInfo?.avatar) {
        if (this.userInfo.avatar.includes('/pc/syncustomconsole/')) {
          try {
            let result = await _API_getSignedUrl({
              url: decodeURIComponent(this.userInfo.avatar),
            });
            return result?.result || DEFAULT_AVATAR_URL;
          } catch (error) {
            console.error('获取头像失败:', error);
            return DEFAULT_AVATAR_URL;
          }
        } else {
          return this.userInfo.avatar;
        }
      } else {
        return DEFAULT_AVATAR_URL;
      }
    },
    setToken(token) {
      this.token = token;
    },
    setRefreshToken(refresh_token) {
      this.refresh_token = refresh_token;
    },
    setUserInfo(userInfo) {
      this.userInfo = userInfo;
    },
    setPageParams(pageParams) {
      this.pageParams = pageParams;
    },
    setCaseCode(caseCode) {
      this.caseCode = caseCode;
    },
    setProcessingId(processingId) {
      this.processingId = processingId;
    },
    setSaveCaseData(saveCaseData) {
      this.saveCaseData = saveCaseData;
    },
    setVersion(version) {
      this.version = version;
    },
  },
  persist: {
    enabled: true,
    key: `${LOCAL_STORAGE_PREFIX_KEY}_user`,
    storage: storage(),
  },
});
