import { defineStore } from 'pinia';

export const useCaseStatusStore = defineStore('caseStatusStore', {
  state: () => ({
    statusMap: {
      drawContent: false, //是否有墙体
      hasBackgroundImage: false, // 是否有底图
      isSelectGoods: false, // 是否选择了商品
      hasSpace: false, // 是否有闭合空间
      hasIntelligentGoods: false, // 是否有智能设备
      quotation: false, // 是否生成报价清单
      canUndo: false, // 是否能撤销
      canRedo: false, // 是否能重做,
      isPlanChanged: false, //方案是否变更
      isSpaceHasIntelligentGoods: false, // 至少有一个房间含有智控设备
    }, // 各种状态的集合
  }),
  actions: {
    /**
     * @description 设置方案状态
     * @param status
     */
    setStatusMap(status) {
      // 只有在对象值不一样时才更新
      if (JSON.stringify(this.statusMap) !== JSON.stringify(status)) {
        this.statusMap = status;
      }
    },
  },
});
