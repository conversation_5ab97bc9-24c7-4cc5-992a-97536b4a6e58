import { nextTick, createApp, h } from 'vue';
import { Tooltip } from 'ant-design-vue';

// 自定义指令：文字一行显示，超出时显示 tooltip
export const vEllipsisTooltip = {
  mounted(el, binding) {
    const { value } = binding;

    // 检查是否需要 tooltip
    nextTick(() => {
      wrapWithTooltip(el, value);
    });
  },

  updated(el, binding) {
    const { value } = binding;
    // 更新时重新检查
    nextTick(() => {
      wrapWithTooltip(el, value);
    });
  },

  unmounted(el) {
    // 清理 tooltip 实例和容器
    if (el._tooltipInstance) {
      el._tooltipInstance.unmount();
      el._tooltipInstance = null;
    }
    if (el._tooltipContainer) {
      el._tooltipContainer.remove();
      el._tooltipContainer = null;
    }
  },
};

// 包装元素并设置 tooltip
function wrapWithTooltip(el, options) {
  el.style.display = '';
  const text = el.textContent || el.innerText;
  const isOverflow = el.scrollWidth > el.clientWidth;

  // 如果已经存在 tooltip 实例，先移除
  if (el._tooltipInstance) {
    el._tooltipInstance.unmount();
    el._tooltipInstance = null;
  }

  if (isOverflow) {
    // 如果不存在 tooltipContainer，则创建并保存
    if (!el._tooltipContainer) {
      const tooltipContainer = document.createElement('div');
      tooltipContainer.style.display = 'contents'; // 元素本身不生成盒模型，子元素直接参与父级布局
      el._tooltipContainer = tooltipContainer;
      el.parentNode.insertBefore(tooltipContainer, el);
    }
    // 主要解决虚拟dom不能访问scoped的css
    const clonedEl = el.cloneNode(true);

    // 创建 tooltip 实例
    const tooltipApp = createApp({
      render() {
        return h(
          Tooltip,
          {
            placement: options?.placement || 'top',
            color: options?.color,
            overlayClassName: options?.overlayClassName,
          },
          {
            default: () => h('div', { ref: 'originalElement', style: { width: `100%`}}), // 将原始内容放入 default 插槽
            title: () => h('span', {}, text),
          }
        );
      },
      mounted() {
        this.$refs.originalElement.appendChild(clonedEl);
      },
    });

    tooltipApp.mount(el._tooltipContainer);

    // 隐藏原始元素
    el.style.display = 'none';

    // 保存实例以便后续清理
    el._tooltipInstance = tooltipApp;
  } else {
    // 如果不溢出，显示原始内容并移除 tooltipContainer
    el.style.display = ''; // 确保原始元素可见
    if (el._tooltipContainer) {
      el._tooltipContainer.remove();
      el._tooltipContainer = null;
    }
  }
}

// 辅助函数：将 CSSStyleDeclaration 转换为普通对象
// function getStyleObject(style) {
//   const styleObject = {};
//   for (let i = 0; i < style.length; i++) {
//     if (style[i] === 'display' && style.getPropertyValue(property) === 'none') continue; // 跳过 length 属性
//     const property = style[i];
//     styleObject[property] = style.getPropertyValue(property);
//   }
//   return styleObject;
// }

// 导出所有指令
export const directives = {
  'ellipsis-tooltip': vEllipsisTooltip,
};
