module.exports = {
  root: true,
  env: {
    node: true,
    'vue/setup-compiler-macros': true,
  },
  plugins: ['vue'],
  extends: ['plugin:vue/vue3-essential', 'eslint:recommended'],
  parserOptions: {
    parser: '@babel/eslint-parser',
    requireConfigFile: false,
  },
  rules: {
    'no-undef': 'error',
    'no-use-before-define': ['error', { functions: false, classes: true }],
    'constructor-super': 'off',
    'no-class-assign': 'off',
    'no-compare-neg-zero': 'off',
    'no-control-regex': 'off',
    'no-debugger': 'off',
    'no-empty-character-class': 'off',
    'no-ex-assign': 'off',
    'no-invalid-regexp': 'off',
    'no-misleading-character-class': 'off',
    'no-new-symbol': 'off',
    'no-prototype-builtins': 'off',
    'no-unreachable': 'off',
    'no-unsafe-negation': 'off',
    'no-useless-backreference': 'off',
    'use-isnan': 'off',
    'no-case-declarations': 'off',
    'no-delete-var': 'off',
    'no-empty': 'off',
    'no-extra-boolean-cast': 'off',
    'no-extra-semi': 'off',
    'no-nonoctal-decimal-escape': 'off',
    'no-octal': 'off',
    'no-redeclare': 'off',
    'no-regex-spaces': 'off',
    'no-shadow-restricted-names': 'off',
    'no-unused-labels': 'off',
    'no-useless-catch': 'off',
    'no-useless-escape': 'off',
    'no-with': 'off',
    'require-yield': 'off',
    'no-mixed-spaces-and-tabs': 'off',
    /* vue2 vue3 公有的禁用规则 */
    'vue/multi-word-component-names': 'off',
    'vue/no-ref-as-operand': 'off',
    'vue/no-reserved-component-names': 'off',
    'vue/no-reserved-keys': 'off',
    'vue/no-unused-components': 'off',
    'vue/no-unused-vars': 'off',
    'vue/no-use-v-if-with-v-for': 'off',
    'vue/require-prop-type-constructor': 'off',
    'vue/require-valid-default-prop': 'off',
    'vue/use-v-on-exact': 'off',
    'vue/no-mutating-props': 'off',
    'vue/no-setup-props-destructure': 'off',
    'vue/no-dupe-keys': 'off',
    'vue/no-useless-template-attributes': 'off',
    /* vue3 特有的禁用规则 */
    'vue/one-component-per-file': 'off',
    'vue/prefer-import-from-vue': 'off',
    'vue/no-watch-after-await': 'off',
    'vue/no-lifecycle-after-await': 'off',
    'vue/no-deprecated-v-is': 'off',
    'vue/no-deprecated-slot-scope-attribute': 'off',
    'vue/no-deprecated-slot-attribute': 'off',
    'vue/no-deprecated-scope-attribute': 'off',
  },
};
